// schema/cashbill.ts
import { z } from "zod";

// Individual product schema for cash bill
export const cashBillProductSchema = z
    .object({
        productcode: z.number(),
        productname: z.string(),
        quantity: z
            .number()
            .min(0.0001, "Quantity must be greater than 0")
            .max(9999.9999, "Quantity cannot exceed 9999.9999")
            .refine((val: number) => {
                // Check if number has more than 4 decimal places
                const decimalPlaces = (val.toString().split(".")[1] || "")
                    .length;
                return decimalPlaces <= 4;
            }, "Quantity can have maximum 4 decimal places"),
        freeQuantity: z
            .number()
            .nonnegative()
            .max(9999.9999, "Free quantity cannot exceed 9999.9999")
            .refine((val: number) => {
                // Check if number has more than 4 decimal places
                const decimalPlaces = (val.toString().split(".")[1] || "")
                    .length;
                return decimalPlaces <= 4;
            }, "Free quantity can have maximum 4 decimal places"),
        gstflag: z.number(),
        gstrate: z
            .number()
            .nonnegative()
            .max(100, "GST rate cannot exceed 100%"),
        pricePerUnit: z.number().nonnegative().default(0.0),
        gstamount: z.number().nonnegative().default(0.0),
        taxableAmount: z.number().nonnegative().default(0.0),
        productAmount: z.number().nonnegative().default(0.0),
        gsflag: z.number(),
        discountAmount: z.number().nonnegative().default(0.0),
        discountPercent: z
            .number()
            .nonnegative()
            .max(100, "Discount percentage cannot exceed 100%")
            .default(0.0),
    })
    .superRefine((data, ctx) => {
        // Validate that free quantity is not greater than main quantity
        if (data.freeQuantity > data.quantity) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message:
                    "Free quantity cannot be greater than the main quantity",
                path: ["freeQuantity"],
            });
        }
    });

// Payment method enum for cash bills (no credit allowed)
export const CashBillPaymentModeEnum = z.enum(["Cash", "Bank", "Online"]);

// Cash bill payload schema
export const cashBillPayloadSchema = z
    .object({
        // Cash bill specific flags
        icflag: z.literal(3), // Cash bill flag
        inoutflag: z.union([z.literal(9), z.literal(15)]), // 9: Purchase, 15: Sales

        // Header fields
        invoiceno: z
            .string()
            .min(1, "Cash bill number is required")
            .max(16, "Cash bill number cannot exceed 16 characters")
            .regex(
                /^(?![0\-\/])[A-Za-z0-9\-\/]{1,16}$/,
                "Cash bill number must be 1–16 characters. Cannot start with 0, - or /. Only A-Z, 0-9, -, / allowed."
            ),

        invoicedate: z
            .string()
            .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),

        // Line items
        contents: z
            .array(cashBillProductSchema)
            .min(1, "At least one product/service is required"),

        // Financial fields
        nettotal: z.number().nonnegative().default(0.0),
        invoicetotal: z.number().nonnegative().default(0.0),
        amountpaid: z.number().nonnegative().default(0.0),

        // Payment details (immediate settlement required)
        paymentmode: z.union([z.literal(2), z.literal(3)]), // 2: Bank/Online, 3: Cash
        paymentMethod: CashBillPaymentModeEnum,

        // Organization details
        orgcode: z.number(),
        taxstate: z.string(),
        sourcestate: z.string(),

        // Optional fields
        invnarration: z
            .string()
            .max(255, "Narration cannot exceed 255 characters")
            .optional(),
        invoicetotalword: z.string(),

        // Cash bill specific flags
        discflag: z.union([z.literal(1), z.literal(16)]),
        roundoffflag: z.number().optional().default(0),
        reversecharge: z.number().optional().default(0),

        // Validation: Amount paid should equal invoice total for cash bills
    })
    .refine(
        (data) => {
            return Math.abs(data.invoicetotal - data.amountpaid) < 0.01;
        },
        {
            message: "For cash bills, amount paid must equal invoice total",
            path: ["amountpaid"],
        }
    );

// Extract cash bill number validation schema for reuse in forms
export const cashBillNumberSchema = z
    .string()
    .min(1, "Cash bill number is required")
    .max(16, "Cash bill number cannot exceed 16 characters")
    .regex(
        /^(?![0\-\/])[A-Za-z0-9\-\/]{1,16}$/,
        "Cash bill number must be 1–16 characters. Cannot start with 0, - or /. Only A-Z, 0-9, -, / allowed."
    );

// Form values interface for cash bill
export interface CashBillFormValues {
    cashBillNo: string;
    cashBillDate: Date;
    paymentMethod: "Cash" | "Bank" | "Online";
    narration: string;
}

// Cash bill item interface
export interface CashBillItem {
    id: number;
    product: any | null;
    hsn: string;
    qty: number;
    freeQty: number;
    amount: number;
    discount: number;
    taxableValue: number;
    gstRate: number;
    gstAmount: number;
    total: number;
}

export type CashBillPayload = z.infer<typeof cashBillPayloadSchema>;
export type CashBillProduct = z.infer<typeof cashBillProductSchema>;
export type CashBillPaymentModeType = z.infer<typeof CashBillPaymentModeEnum>;
