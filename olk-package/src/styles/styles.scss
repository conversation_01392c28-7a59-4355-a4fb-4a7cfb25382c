.nav_button {
    background: white !important;
    color: #1d4ed8;
    border-color: #1d4ed8;
    padding: 1rem 2rem;
    box-shadow: none;
    font-size: large;
    display: inline-flex; /* Ensure the button itself is a flex container */
    align-items: center; /* Center items vertically */
    gap: 0.5rem; /* Adjust spacing between icon and label */

    .pi {
        color: #1d4ed8;
    }

    @media (max-width: 786px) {
        width: 45% !important;
        flex-direction: row; /* Change to row to keep icon and label side by side */
        gap: 0.5rem; /* Adjust gap for mobile */
        padding: 0.75rem 1rem; /* Optional: Reduce padding for better fit on mobile */
    }
}

@media (max-width: 640px) {
    .icon-tight-gap .p-button-icon {
        margin-right: 0.25rem !important; // Reduce gap (default is usually 0.5rem)
    }
}

.custom-divider {
    height: 0.5px;
    background-color: #1d4ed8;
}

.p-chart {
    height: 250px;
}
.chart-dash {
    width: 80%;
    @media (max-width: 786px) {
        width: 100%;
    }
}

.s-btns {
  width: 100%; /* Ensure the card takes full available width */
  box-sizing: border-box; /* Include padding and border in width calculations */
}

.s-btns .mt-2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  overflow: hidden; /* Prevent content from overflowing */
  flex-wrap: nowrap; /* Prevent wrapping to keep icon and text in one line */
}

.s-btns .pi-angle-right {
  flex-shrink: 0; /* Prevent the icon from shrinking */
  margin-left: 0.5rem; /* Ensure spacing between text and icon */
}

@media (max-width: 786px) {
  .s-btns {
    padding: 1rem; /* Reduce padding on mobile for more space */
  }

  .s-btns .mt-2 {
    font-size: 0.9rem; /* Reduce font size slightly for mobile */
    gap: 0.25rem; /* Reduce gap for tighter layout */
  }

  .s-btns .pi-angle-right {
    font-size: 14px !important; /* Slightly smaller icon on mobile */
    margin-left: 0.25rem; /* Adjust spacing */
  }
}
