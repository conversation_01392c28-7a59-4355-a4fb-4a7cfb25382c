"use client";

import React, { useRef, useState, useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { But<PERSON> } from "primereact/button";
import { Card } from "primereact/card";
import { Checkbox } from "primereact/checkbox";
import { FileUpload } from "primereact/fileupload";
import { Toast } from "primereact/toast";
import { useRouter } from "next/navigation";
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import { getOrgDataField } from "../../../utils/cookies";
//import axiosInstance from "../../../utils/axios";
import { InputTextarea } from "primereact/inputtextarea";
import apiCall from '../../../utils/apiCallService';   


interface InvoicePreferencesFormData {
    logo?: File;
    sign?: File;
    autoinvno: boolean;
    terms?: string;
}

interface InvoicePreferencesProps {
    onSuccess?: () => void;
}

interface InvoicePrefs {
    logo?: string;
    sign?: string;
    autoinvno?: number;
    tandc?: string;
}

interface OrgFields {
    bankdetails: any;
    gstin: string;
    orgpan: string;
    orgaddr: string;
    orgpincode: string;
    invoice_preferences?: InvoicePrefs;
}

interface OrgDetails {
    bisdetails: OrgFields;
    olkstatus?: number;
}

interface InvPrefApi {
  olkstatus: number;
  message?: string;
}

const InvoicePreferencesForm: React.FC<InvoicePreferencesProps> = ({
    onSuccess,
}) => {
    const { control, handleSubmit, reset, setValue } =
        useForm<InvoicePreferencesFormData>({
            defaultValues: {
                autoinvno: false,
                terms: "",
            },
        });

    // Helper function to detect image format from base64 data
    const detectImageFormat = (base64String: string): string => {
        // Check the first few characters of base64 to detect format
        const header = base64String.substring(0, 20);

        // Common image format signatures in base64
        if (header.startsWith("/9j/") || header.startsWith("iVBORw0KGgo")) {
            return header.startsWith("/9j/") ? "jpeg" : "png";
        }

        // Try to decode and check magic bytes and content
        try {
            const binaryString = atob(base64String.substring(0, 100)); // Check more bytes for SVG
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            // PNG signature: 89 50 4E 47
            if (
                bytes[0] === 0x89 &&
                bytes[1] === 0x50 &&
                bytes[2] === 0x4e &&
                bytes[3] === 0x47
            ) {
                return "png";
            }
            // JPEG signature: FF D8 FF
            if (bytes[0] === 0xff && bytes[1] === 0xd8 && bytes[2] === 0xff) {
                return "jpeg";
            }

            // SVG detection - check for SVG content in decoded string
            const decodedContent = binaryString.toLowerCase();
            if (
                decodedContent.includes("<svg") ||
                decodedContent.includes("<?xml")
            ) {
                return "svg+xml";
            }
        } catch (e) {
            console.warn("Could not detect image format, defaulting to png");
        }

        return "png"; // Default fallback
    };

    // Helper function to create proper data URL with format detection
    const createImageDataUrl = (base64String: string): string => {
        const format = detectImageFormat(base64String);
        // Handle SVG format properly
        const mimeType =
            format === "svg+xml" ? "image/svg+xml" : `image/${format}`;
        return `data:${mimeType};base64,${base64String}`;
    };

    const [loading, setLoading] = useState(false);
    const [fetchingData, setFetchingData] = useState(true);
    const [logoFile, setLogoFile] = useState<File | null>(null);
    const [signFile, setSignFile] = useState<File | null>(null);
    const [orgData, setOrgData] = useState<OrgDetails | null>(null);
    const [existingLogo, setExistingLogo] = useState<string | null>(null);
    const [existingSign, setExistingSign] = useState<string | null>(null);
    const [removeLogo, setRemoveLogo] = useState(false);
    const [removeSign, setRemoveSign] = useState(false);
    const toast = useRef<Toast>(null);
    const router = useRouter();

    // Refs for FileUpload components to clear them after successful save
    const logoUploadRef = useRef<any>(null);
    const signUploadRef = useRef<any>(null);
    const { OLK_PATH } = useEnvContext();
    const orgcode = Number(getOrgDataField("orgcode"));

    // File size validation (256KB = 262144 bytes)
    const MAX_FILE_SIZE = 262144;

    // Fetch organization data on component mount
    useEffect(() => {
        const fetchOrgData = async () => {
            try {
                setFetchingData(true);
                const response = await apiCall<OrgDetails>("GET", `${OLK_PATH}/organisations/bdt?orgcode=${orgcode}`);

                const data = response.data;

                if (data.olkstatus === 0) {
                    setOrgData(data);

                    // Store existing logo and signature
                    if (data.bisdetails?.invoice_preferences) {
                        const prefs = data.bisdetails.invoice_preferences;

                        // Set logo state (null if not exists)
                        setExistingLogo(prefs.logo || null);

                        // Set signature state (null if not exists)
                        setExistingSign(prefs.sign || null);

                        // Pre-populate form with existing data
                        reset({
                            autoinvno: prefs.autoinvno === 1,
                            terms: prefs.tandc || "",
                        });

                       
                    } else {
                        // No preferences exist, reset everything
                        setExistingLogo(null);
                        setExistingSign(null);
                        reset({
                            autoinvno: false,
                            terms: "",
                        });
                        console.log("No preferences found, reset to defaults");
                    }
                } else {
                    toast.current?.show({
                        severity: "error",
                        summary: "Error",
                        detail: "Failed to fetch organization details",
                        life: 3000,
                    });
                }
            } catch (error) {
                console.error("Error fetching organization data:", error);
                toast.current?.show({
                    severity: "error",
                    summary: "Error",
                    detail: "Failed to fetch organization details",
                    life: 3000,
                });
            } finally {
                setFetchingData(false);
            }
        };

        if (orgcode) {
            fetchOrgData();
        }
    }, [orgcode, OLK_PATH, reset]);

    const validateFileSize = (file: File): boolean => {
        return file.size <= MAX_FILE_SIZE;
    };

    const convertToBase64 = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
                const result = reader.result as string;
                // Remove the data:image/...;base64, prefix
                const base64 = result.split(",")[1];
                resolve(base64);
            };
            reader.onerror = (error) => reject(error);
        });
    };

    const onLogoSelect = (event: any) => {
        const file = event.files[0];
        if (file) {
            if (!validateFileSize(file)) {
                toast.current?.show({
                    severity: "error",
                    summary: "Error",
                    detail: "Logo file size must be less than 256KB",
                    life: 3000,
                });
                return;
            }
            setLogoFile(file);
        }
    };

    const onSignSelect = (event: any) => {
        const file = event.files[0];
        if (file) {
            if (!validateFileSize(file)) {
                toast.current?.show({
                    severity: "error",
                    summary: "Error",
                    detail: "Sign file size must be less than 256KB",
                    life: 3000,
                });
                return;
            }
            setSignFile(file);
        }
    };

    const onSubmit = async (data: InvoicePreferencesFormData) => {
        setLoading(true);

        try {
            const payload: any = {
                orgcode: orgcode,
                invprefs: {},
            };

            // Handle logo: use new upload, preserve existing, or remove
            if (logoFile) {
                const logoBase64 = await convertToBase64(logoFile);
                payload.invprefs.logo = logoBase64;
                setExistingLogo(logoBase64); // Update existing logo
            } else if (removeLogo) {
                // Remove logo by sending empty string
                payload.invprefs.logo = "";
            } else if (existingLogo) {
                // Preserve existing logo if no new file uploaded and not removing
                payload.invprefs.logo = existingLogo;
            }

            // Handle signature: use new upload, preserve existing, or remove
            if (signFile) {
                const signBase64 = await convertToBase64(signFile);
                payload.invprefs.sign = signBase64;
                setExistingSign(signBase64); // Update existing signature
            } else if (removeSign) {
                // Remove signature by sending empty string
                payload.invprefs.sign = "";
            } else if (existingSign) {
                // Preserve existing signature if no new file uploaded and not removing
                payload.invprefs.sign = existingSign;
            }

            // Add auto invoice number preference (always include)
            payload.invprefs.autoinvno = data.autoinvno ? 1 : 0;

            // Add terms and conditions (always include, even if empty)
            payload.invprefs.tandc = data.terms || "";

            const response = await apiCall<InvPrefApi>("PUT",`${OLK_PATH}/organisations/setprefs`,payload)

            if (response.data?.olkstatus === 0 || response.status === 200) {
                toast.current?.show({
                    severity: "success",
                    summary: "Success",
                    detail: "Invoice preferences saved successfully",
                    life: 3000,
                });

                // Fetch updated data to refresh the view
                fetchUpdatedData();

                // Reset form state but keep existing values
                setLogoFile(null);
                setSignFile(null);
                setRemoveLogo(false);
                setRemoveSign(false);

                // Clear FileUpload components to remove any file previews
                if (logoUploadRef.current) {
                    logoUploadRef.current.clear();
                }
                if (signUploadRef.current) {
                    signUploadRef.current.clear();
                }

                if (onSuccess) {
                    onSuccess();
                }
            } else {
                throw new Error("Failed to save preferences");
            }
        } catch (error) {
            console.error("Error saving preferences:", error);
            toast.current?.show({
                severity: "error",
                summary: "Error",
                detail: "Failed to save invoice preferences. Please try again.",
                life: 3000,
            });
        } finally {
            setLoading(false);
        }
    };

    // Fetch updated data after saving
    const fetchUpdatedData = async () => {
        try {

           const response = await apiCall<OrgDetails>("GET", `${OLK_PATH}/organisations/bdt?orgcode=${orgcode}`);
            const data = response.data;

            if (data.olkstatus === 0) {
                setOrgData(data);

                // Update existing logo and signature
                if (data.bisdetails?.invoice_preferences) {
                    const prefs = data.bisdetails.invoice_preferences;

                    // Set logo state (null if not exists)
                    setExistingLogo(prefs.logo || null);

                    // Set signature state (null if not exists)
                    setExistingSign(prefs.sign || null);

                    // Update form values
                    reset({
                        autoinvno: prefs.autoinvno === 1,
                        terms: prefs.tandc || "",
                    });
                } else {
                    // No preferences exist, reset everything
                    setExistingLogo(null);
                    setExistingSign(null);
                    reset({
                        autoinvno: false,
                        terms: "",
                    });
                }
            }
        } catch (error) {
            console.error("Error fetching updated data:", error);
        }
    };

    const handleRemoveLogo = () => {
        setRemoveLogo(true);
        setExistingLogo(null);
    };

    const handleRemoveSign = () => {
        setRemoveSign(true);
        setExistingSign(null);
    };

    const handleReset = () => {
        reset();
        setLogoFile(null);
        setSignFile(null);
        setRemoveLogo(false);
        setRemoveSign(false);

        // Clear FileUpload components
        if (logoUploadRef.current) {
            logoUploadRef.current.clear();
        }
        if (signUploadRef.current) {
            signUploadRef.current.clear();
        }
    };

    if (fetchingData) {
        return (
            <div
                className="flex justify-content-center align-items-center"
                style={{ minHeight: "200px" }}
            >
                <i
                    className="pi pi-spin pi-spinner"
                    style={{ fontSize: "2rem" }}
                ></i>
            </div>
        );
    }

    return (
        <>
            <Toast ref={toast} />
            <style
                dangerouslySetInnerHTML={{
                    __html: `

                    .preview-container {
                        margin-top: 10px;
                        border: 1px solid #e0e0e0;
                        border-radius: 4px;
                        padding: 10px;
                        background-color: #f9f9f9;
                    }
                    .preview-image {
                        max-width: 200px;
                        max-height: 100px;
                        object-fit: contain;
                        border: 1px solid #ddd;
                        padding: 4px;
                        background: white;
                    }

                    /* Responsive image sizing */
                    .responsive-image {
                        object-fit: contain;
                        display: block;
                    }

                    /* Large screens - more prominent sizing */
                    @media (min-width: 1024px) {
                        .responsive-image {
                            max-width: 300px;
                            max-height: 150px;
                        }
                    }

                    /* Medium screens - intermediate sizing */
                    @media (min-width: 768px) and (max-width: 1023px) {
                        .responsive-image {
                            max-width: 250px;
                            max-height: 125px;
                        }
                    }

                    /* Small screens - full width for better visibility */
                    @media (max-width: 767px) {
                        .responsive-image {
                            width: 100%;
                            max-width: 100%;
                            height: auto;
                            max-height: 150px;
                        }
                    }
                    .image-container {
                        position: relative;
                        display: inline-block;
                    }
                    .remove-button {
                        position: absolute;
                        top: -16px;
                        right: -12px;
                        background: white;
                        color: #dc3545;
                        // border: 2px solid #dc3545;
                        border: none;
                        border-radius: 50%;
                        width: 28px;
                        height: 28px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        font-size: 20px;
                        font-weight: bold;
                        
                        z-index: 10;
                        transition: all 0.2s ease;
                    }
                    .remove-button:hover {
                        // background: #dc3545;
                        color: #dc3545;
                        transform: scale(1.05);
                        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
                    }
                    .remove-button:active {
                        transform: scale(0.95);
                    }

                    /* Mobile responsive styles */
                    @media (max-width: 768px) {
                        .remove-button {
                            width: 36px;
                            height: 36px;
                            top: -8px;
                            right: -8px;
                            font-size: 20px;
                            border-width: 2px;
                            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
                        }
                        .image-container {
                            width: 100%;
                        }
                    }

                    /* Touch devices - larger touch targets */
                    @media (hover: none) and (pointer: coarse) {
                        .remove-button {
                            width: 36px;
                            height: 36px;
                            font-size: 20px;
                            top: -2px;
                            right: -2px;
                            border-width: 3px;
                        }
                    }

                    /* Very small screens */
                    @media (max-width: 480px) {
                        .remove-button {
                            width: 24px;
                            height: 24px;
                            font-size: 14px;
                            top: -4px;
                            right: -4px;
                        }
                        .responsive-image {
                            max-height: 120px !important;
                        }
                    }

                    /* Action buttons responsive styles */
                    .action-buttons-container {
                        display: flex;
                        gap: 12px;
                        flex-wrap: wrap;
                        justify-content: flex-start;
                        margin-top: 1rem;
                    }

                    @media (max-width: 768px) {
                        .action-buttons-container {
                            flex-direction: column;
                            gap: 8px;
                            align-items: stretch;
                        }
                        .action-buttons-container .p-button {
                            width: 100%;
                            justify-content: center;
                            padding: 12px 16px;
                            font-size: 14px;
                        }
                        .action-buttons-container .p-button .p-button-icon {
                            margin-right: 8px;
                        }
                    }

                    @media (max-width: 480px) {
                        .action-buttons-container .p-button {
                            padding: 14px 20px;
                            font-size: 16px;
                            font-weight: 600;
                        }
                        .action-buttons-container .p-button .p-button-icon {
                            font-size: 16px;
                        }
                    }
                `,
                }}
            />
            <Card className="p-4">
                <div className="flex justify-content-between align-items-center mb-4">
                    <h2 className="text-xl md:text-2xl font-semibold">
                        Invoice Preferences
                    </h2>
                </div>

                <form onSubmit={handleSubmit(onSubmit)} className="grid">
                    {/* Logo Upload */}
                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label htmlFor="logo" className="font-semibold">
                                Company Logo
                            </label>
                            <div className="mt-2">
                                <FileUpload
                                    ref={logoUploadRef}
                                    name="logo"
                                    accept="image/*"
                                    maxFileSize={MAX_FILE_SIZE}
                                    onSelect={onLogoSelect}
                                    onClear={() => setLogoFile(null)}
                                    chooseLabel="Upload Logo"
                                    auto={true}
                                    customUpload={true}
                                    multiple={false}
                                    className="w-full"
                                />
                                <small className="text-muted">
                                    The only image formats allowed are JPG,
                                    JPEG, SVG, and PNG, with a maximum file size
                                    of 256 KB
                                </small>

                                {existingLogo && !logoFile && (
                                    <div className="mt-3">
                                        <p className="text-sm text-gray-600 mb-2">
                                            Current Logo:
                                        </p>
                                        <div className="image-container">
                                            <img
                                                src={createImageDataUrl(
                                                    existingLogo
                                                )}
                                                alt="Current Logo"
                                                className="border-1 border-gray-300 border-round p-2 responsive-image"
                                            />
                                            <button
                                                type="button"
                                                className="remove-button"
                                                onClick={handleRemoveLogo}
                                                title="Remove logo"
                                            >
                                                ×
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Sign Upload */}
                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label htmlFor="sign" className="font-semibold">
                                Signature
                            </label>
                            <div className="mt-2">
                                <FileUpload
                                    ref={signUploadRef}
                                    name="sign"
                                    accept="image/*"
                                    maxFileSize={MAX_FILE_SIZE}
                                    onSelect={onSignSelect}
                                    onClear={() => setSignFile(null)}
                                    chooseLabel="Upload Signature"
                                    auto={true}
                                    customUpload={true}
                                    multiple={false}
                                    className="w-full"
                                />
                                <small className="text-muted">
                                    The only image formats allowed are JPG,
                                    JPEG, SVG, and PNG, with a maximum file size
                                    of 256 KB
                                </small>

                                {existingSign && !signFile && (
                                    <div className="mt-3">
                                        <p className="text-sm text-gray-600 mb-2">
                                            Current Signature:
                                        </p>
                                        <div className="image-container">
                                            <img
                                                src={createImageDataUrl(
                                                    existingSign
                                                )}
                                                alt="Current Signature"
                                                className="border-1 border-gray-300 border-round p-2 responsive-image"
                                            />
                                            <button
                                                type="button"
                                                className="remove-button"
                                                onClick={handleRemoveSign}
                                                title="Remove signature"
                                            >
                                                ×
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Auto Invoice Number */}
                    <div className="col-12">
                        <div className="field">
                            <Controller
                                name="autoinvno"
                                control={control}
                                render={({ field }) => (
                                    <div className="flex align-items-center">
                                        <Checkbox
                                            inputId="autoinvno"
                                            checked={field.value}
                                            onChange={(e) =>
                                                field.onChange(e.checked)
                                            }
                                        />
                                        <label
                                            htmlFor="autoinvno"
                                            className="ml-2 font-semibold"
                                        >
                                            Auto Generate Invoice Numbers
                                        </label>
                                    </div>
                                )}
                            />
                        </div>
                    </div>

                    {/* Terms and Conditions */}
                    <div className="col-12">
                        <div className="field">
                            <label htmlFor="terms" className="font-semibold">
                                Terms and Conditions
                            </label>
                            <Controller
                                name="terms"
                                control={control}
                                render={({ field }) => (
                                    <InputTextarea
                                        id="terms"
                                        {...field}
                                        placeholder="Enter terms and conditions (optional)"
                                        className="w-full mt-2"
                                        // rows={4}
                                        autoResize
                                    />
                                )}
                            />
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="col-12">
                        <div className="action-buttons-container">
                            <Button
                                type="submit"
                                label="Save Preferences"
                                icon="pi pi-save"
                                className="p-button-success"
                                loading={loading}
                            />
                            <Button
                                type="button"
                                label="Reset"
                                icon="pi pi-undo"
                                className="p-button-secondary"
                                onClick={handleReset}
                                disabled={loading}
                            />
                        </div>
                    </div>
                </form>
            </Card>
        </>
    );
};

export default InvoicePreferencesForm;
