"use client";
import React from 'react'

interface SummaryCardItem {
    custname?: string; // For customers and suppliers
    productdesc?: string; // For products/services
    totalamount?: string; // For customers/suppliers
    ttlrevenue?: string; // For products/services
}

interface SummaryCardProps {
    title: string;
    data?: SummaryCardItem[];
}

const SummaryCard: React.FC<SummaryCardProps> = ({ title, data = [] }) => {
    return (
        <div
            className="w-full md:flex-1 border-1
            border-blue-300 border-round-xl bg-white pb-2"
        >
            <h2 className="text-lg font-semibold px-2 pt-2">{title}</h2>
            <div className="w-full">
                <div className="flex justify-content-between font-semibold bg-gray-100 px-2 py-1 rounded-sm">
                    <span>Name</span>
                    <span>Amount</span>
                </div>
                <div className="mt-2 flex flex-column gap-3">
                    {data.map((item, idx) => {
                        const name = item.custname || item.productdesc || "Unknown";
                        const amount = item.totalamount || item.ttlrevenue || "0.00";
                        return (
                            <div
                                key={idx}
                                className="flex justify-content-between text-sm px-2"
                            >
                                <span className="truncate max-w-[60%]">{name}</span>
                                <span>₹ {Number(amount ?? 0).toLocaleString(
                                        "en-IN",
                                        {
                                            minimumFractionDigits: 2,
                                            maximumFractionDigits: 2,
                                        }
                                    )}
                                </span>

                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default SummaryCard;
