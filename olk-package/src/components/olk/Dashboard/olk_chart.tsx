"use client";
import apiCall from '../../../utils/apiCallService';
import { useEnvContext } from '../../../contexts/EnvContextProvider';
import { getOrgDataField } from '../../../utils/cookies';
import { Chart } from 'primereact/chart';
import { useEffect, useMemo, useState } from 'react';

type MonthlyPurchaseItem = {
    month: string;
    'total purchase': number;
};

type MonthlySalesItem = {
    month: string;
    'total sales': number;
};

type DataMap = {
    [month: string]: {
        sales: number;
        purchase: number;
    };
};

const BarChartComponent = () => {
    const [chartData, setChartData] = useState<any>(null); // Use ChartData type if you want to strictly type
    const { OLK_PATH } = useEnvContext();
    const orgCode = Number(getOrgDataField('orgcode'));
    const isoString1 = getOrgDataField('yearstart');
    const yearstart = isoString1 ? new Date(isoString1).toISOString().split('T')[0] : '';
    const isoString = getOrgDataField('yearend');
    const yearend = isoString ? new Date(isoString).toISOString().split('T')[0] : '';

    useEffect(() => {
        const fetchData = async () => {
            try {
                const [purchaseRes, salesRes] = await Promise.all([
                    apiCall<{ 'monthly purchase': MonthlyPurchaseItem[] }>(
                        "GET",
                        `${OLK_PATH}/dashboard/monthlypurchase?orgcode=${orgCode}&financialYearStart=${yearstart}&endDate=${yearend}`,
                    ),
                    apiCall<{ 'monthly report': MonthlySalesItem[] }>(
                        "GET",
                        `${OLK_PATH}/dashboard/monthlysale?orgcode=${orgCode}&financialYearStart=${yearstart}&endDate=${yearend}`,
                    ),
                ]);

                const purchaseRaw = purchaseRes.data['monthly purchase'];
                const salesRaw = salesRes.data['monthly report'];
                const dataMap: DataMap = {};

                // Populate sales
                salesRaw.forEach(item => {
                    const month = item.month;
                    if (!dataMap[month]) {
                        dataMap[month] = { sales: 0, purchase: 0 };
                    }
                    dataMap[month].sales = item['total sales'];
                });

                // Populate purchases
                purchaseRaw.forEach(item => {
                    const month = item.month;
                    if (!dataMap[month]) {
                        dataMap[month] = { sales: 0, purchase: 0 };
                    }
                    dataMap[month].purchase = item['total purchase'];
                });

                // Sort months by date (assumes "MMM YYYY" format like "Apr 2025")
                const sortedMonths = Object.keys(dataMap).sort((a, b) => {
                    const [monthA, yearA] = a.split(' ');
                    const [monthB, yearB] = b.split(' ');

                    const dateA = new Date(`${monthA} 1, ${yearA}`);
                    const dateB = new Date(`${monthB} 1, ${yearB}`);

                    return dateA.getTime() - dateB.getTime();
                });

                const salesData = sortedMonths.map(month => dataMap[month].sales);
                const purchaseData = sortedMonths.map(month => dataMap[month].purchase);

                setChartData({
                    labels: sortedMonths,
                    datasets: [
                        {
                            label: 'Sales',
                            backgroundColor: '#2563eb',
                            data: salesData,
                        },
                        {
                            label: 'Purchases',
                            backgroundColor: '#bfdbfe',
                            data: purchaseData,
                        },
                    ],
                });
            } catch (error) {
                console.error('Error fetching chart data:', error);
            }
        };

        fetchData();
    }, [OLK_PATH, orgCode,yearstart, yearend]);

    const options = useMemo(() => ({
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: {
                    color: '#000000', // gray-700
                },
            },
        },
        scales: {
            x: {
                ticks: {
                    color: '#545454', // gray-500
                },
                grid: {
                    display: false,
                },
            },
            y: {
                beginAtZero: true,
                ticks: {
                    color: '#6B7280',
                },
                grid: {
                    color: '#E5E7EB', // gray-200
                },
            },
        },
    }), []);

    return (
        <div className="mt-6 chart-dash">
            {chartData ? (
                <Chart type="bar" data={chartData} options={options} />
            ) : (
                <p>Loading chart...</p>
            )}
        </div>
    );
};

export default BarChartComponent;
