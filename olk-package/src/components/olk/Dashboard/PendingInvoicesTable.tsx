"use client";

import { usePara<PERSON>, useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { But<PERSON> } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import axiosInstance from "../../../utils/axios";
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import { getOrgDataField } from "../../../utils/cookies";

interface PendingInvoice {
    invid: number;
    invoiceno: string;
    invoicedate: string;
    invoicetotal: string;
    amountpaid: string;
    pendingamount: string;
}

interface InvoiceApiResponse {
    olkstatus: number;
    invoices: PendingInvoice[];
}

const PendingInvoicesTable = () => {
    const params = useParams();
    const searchParams = useSearchParams();
    const router = useRouter();
    const [invoices, setInvoices] = useState<PendingInvoice[]>([]);
    const [loading, setLoading] = useState(false);
    const [partyName, setPartyName] = useState<string>("");
    const { OLK_PATH } = useEnvContext();
    const orgCode = Number(getOrgDataField("orgcode"));

    const partyId = params.partyId as string;
    const type = searchParams.get("type") as "receipts" | "payments";
    const inoutflag = type === "receipts" ? 15 : 9;

    useEffect(() => {
        fetchPendingInvoices();
        fetchPartyDetails();
    }, [partyId]);

    const fetchPartyDetails = async () => {
        try {
            const response = await axiosInstance.get(`/parties`, {
                baseURL: OLK_PATH,
                params: {
                    orgcode: orgCode,
                },
            });

            const data = response.data;
            if (data.olkstatus === 0 && data.olkresult) {
                // Find the party with matching custid
                const party = data.olkresult.find(
                    (p: any) => p.custid.toString() === partyId
                );
                if (party) {
                    setPartyName(party.custname || "Unknown Party");
                }
            }
        } catch (error) {
            console.error("Error fetching party details:", error);
            setPartyName("Unknown Party");
        }
    };

    const fetchPendingInvoices = async () => {
        setLoading(true);
        try {
            const response = await axiosInstance.get(`/invoice/unpdbyparty`, {
                baseURL: OLK_PATH,
                params: {
                    orgcode: orgCode,
                    custid: partyId,
                    inoutflag: inoutflag,
                },
            });

            // console.log("API Response:", response.data);
            const data = response.data as InvoiceApiResponse;
            // console.log("Parsed invoices:", data.invoices);
            setInvoices(data.invoices || []);
        } catch (error) {
            // console.error("Error fetching pending invoices:", error);
            setInvoices([]);
        } finally {
            setLoading(false);
        }
    };

    const handleViewInvoice = (invid: number) => {
        router.push(`/dashboard/invoice/sales-and-purchase/view/${invid}`);
    };

    const formatCurrency = (value: string) => {
        return `₹${Number(value).toLocaleString("en-IN", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        })}`;
    };

    const invoiceNumberTemplate = (rowData: PendingInvoice) => {
        return <span className="font-bold">{rowData.invoiceno}</span>;
    };

    const amountTemplate = (
        rowData: PendingInvoice,
        field: keyof PendingInvoice,
        color: string
    ) => {
        const value = rowData[field];
        const safeValue = value?.toString() || "0";
        return (
            <span className={`font-semibold ${color}`}>
                {formatCurrency(safeValue)}
            </span>
        );
    };

    const actionTemplate = (rowData: PendingInvoice) => {
        const isPending =
            parseFloat(rowData.invoicetotal) > parseFloat(rowData.amountpaid);
        return (
            <div className="flex justify-start items-center w-full gap-2">
                <Button
                    icon="pi pi-eye"
                    className="p-button-sm p-button-text !p-2"
                    onClick={() => handleViewInvoice(rowData.invid)}
                    tooltip="View Invoice"
                    tooltipOptions={{ position: "top" }}
                />
                {/* Conditionally render R/P button for pending invoices */}
                {isPending && type === "receipts" && (
                    <Button
                        label="R"
                        className="p-button-sm p-button-text !p-2"
                        style={{ fontWeight: 700, fontSize: "0.9rem" }}
                        tooltip="Receive Payment"
                        tooltipOptions={{ position: "top" }}
                        onClick={() => {
                            router.push(
                                `/dashboard/invoice/sales-and-purchase/payment-receipt-form?invoiceId=${rowData.invid}&invoiceType=sales`
                            );
                        }}
                        aria-label="Receive Payment"
                    />
                )}
                {isPending && type === "payments" && (
                    <Button
                        label="P"
                        className="p-button-sm p-button-text !p-2"
                        style={{ fontWeight: 700, fontSize: "0.9rem" }}
                        tooltip="Make Payment"
                        tooltipOptions={{ position: "top" }}
                        onClick={() => {
                            router.push(
                                `/dashboard/invoice/sales-and-purchase/payment-receipt-form?invoiceId=${rowData.invid}&invoiceType=purchase`
                            );
                        }}
                        aria-label="Make Payment"
                    />
                )}
            </div>
        );
    };

    return (
        <div className="card">
            <div className="flex justify-content-between align-items-center mb-4">
                <h2 className="text-2xl font-bold">
                    {type === "receipts"
                        ? "Pending Receipts"
                        : "Pending Payments"}
                    {partyName && ` - ${partyName}`}
                </h2>
            </div>

            {loading ? (
                <div className="flex justify-content-center">
                    <i
                        className="pi pi-spin pi-spinner"
                        style={{ fontSize: "2rem" }}
                    ></i>
                </div>
            ) : (
                <>
                    {/* DataTable for Desktop */}
                    <div className="hidden lg:block">
                        <DataTable
                            value={invoices}
                            rows={50}
                            className="p-datatable-sm"
                            emptyMessage="No pending invoices found"
                            showGridlines
                            stripedRows
                            size="small"
                            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                        >
                            <Column
                                field="invoiceno"
                                header="Invoice"
                                body={invoiceNumberTemplate}
                                sortable
                                headerStyle={{ backgroundColor: "#bfdbfe" }}
                            />
                            <Column
                                field="invoicedate"
                                header="Date"
                                sortable
                                headerStyle={{ backgroundColor: "#bfdbfe" }}
                            />
                            <Column
                                field="invoicetotal"
                                header="Total Amount"
                                body={(rowData) =>
                                    amountTemplate(
                                        rowData,
                                        "invoicetotal",
                                        "text-900"
                                    )
                                }
                                sortable
                                headerStyle={{ backgroundColor: "#bfdbfe" }}
                            />
                            <Column
                                field="amountpaid"
                                header="Amount Paid"
                                body={(rowData) => (
                                    <span
                                        className="font-semibold"
                                        style={{ color: "#188926" }}
                                    >
                                        {formatCurrency(rowData.amountpaid)}
                                    </span>
                                )}
                                sortable
                                headerStyle={{ backgroundColor: "#bfdbfe" }}
                            />
                            <Column
                                field="pendingamount"
                                header="Pending Amount"
                                body={(rowData) =>
                                    amountTemplate(
                                        rowData,
                                        "pendingamount",
                                        "text-red-500"
                                    )
                                }
                                sortable
                                headerStyle={{ backgroundColor: "#bfdbfe" }}
                            />
                            <Column
                                header="Action"
                                body={actionTemplate}
                                headerStyle={{
                                    textAlign: "center",
                                    backgroundColor: "#bfdbfe",
                                }}
                            />
                        </DataTable>
                    </div>

                    {/* Mobile Card Layout */}
                    <div className="block lg:hidden">
                        {invoices.length === 0 ? (
                            <div className="text-center text-gray-500 py-8">
                                No pending invoices found
                            </div>
                        ) : (
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                {invoices.map((invoice) => {
                                    const isPending =
                                        parseFloat(invoice.invoicetotal) >
                                        parseFloat(invoice.amountpaid);
                                    return (
                                        <div
                                            key={invoice.invid}
                                            className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"
                                        >
                                            <div className="flex justify-between items-start mb-3">
                                                <div>
                                                    <div className="font-bold text-lg text-gray-800">
                                                        {invoice.invoiceno}
                                                    </div>
                                                    <div className="text-sm text-gray-600">
                                                        {invoice.invoicedate}
                                                    </div>
                                                </div>
                                                <div className="flex gap-2">
                                                    <Button
                                                        icon="pi pi-eye"
                                                        className="p-button-sm p-button-text action"
                                                        onClick={() =>
                                                            handleViewInvoice(
                                                                invoice.invid
                                                            )
                                                        }
                                                        aria-label="View Invoice"
                                                    />
                                                    {isPending &&
                                                        type === "receipts" && (
                                                            <Button
                                                                label="R"
                                                                className="p-button-sm p-button-text !p-2"
                                                                style={{
                                                                    fontWeight: 700,
                                                                    fontSize:
                                                                        "0.9rem",
                                                                }}
                                                                tooltip="Receive Payment"
                                                                onClick={() => {
                                                                    router.push(
                                                                        `/dashboard/invoice/sales-and-purchase/payment-receipt-form?invoiceId=${invoice.invid}&invoiceType=sales`
                                                                    );
                                                                }}
                                                                aria-label="Receive Payment"
                                                            />
                                                        )}
                                                    {isPending &&
                                                        type === "payments" && (
                                                            <Button
                                                                label="P"
                                                                className="p-button-sm p-button-text !p-2"
                                                                style={{
                                                                    fontWeight: 700,
                                                                    fontSize:
                                                                        "0.9rem",
                                                                }}
                                                                tooltip="Make Payment"
                                                                onClick={() => {
                                                                    router.push(
                                                                        `/dashboard/invoice/sales-and-purchase/payment-receipt-form?invoiceId=${invoice.invid}&invoiceType=purchase`
                                                                    );
                                                                }}
                                                                aria-label="Make Payment"
                                                            />
                                                        )}
                                                </div>
                                            </div>
                                            <div className="space-y-2">
                                                <div className="flex justify-between">
                                                    <span className="text-sm text-gray-600">
                                                        Total Amount:
                                                    </span>
                                                    <span className="font-semibold text-gray-800">
                                                        {formatCurrency(
                                                            invoice.invoicetotal
                                                        )}
                                                    </span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-sm text-gray-600">
                                                        Amount Paid:
                                                    </span>
                                                    <span
                                                        className="font-semibold"
                                                        style={{
                                                            color: "#188926",
                                                        }}
                                                    >
                                                        {formatCurrency(
                                                            invoice.amountpaid
                                                        )}
                                                    </span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-sm text-gray-600">
                                                        Pending Amount:
                                                    </span>
                                                    <span className="font-semibold text-red-500">
                                                        {formatCurrency(
                                                            invoice.pendingamount
                                                        )}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        )}
                    </div>
                </>
            )}
        </div>
    );
};

export default PendingInvoicesTable;
