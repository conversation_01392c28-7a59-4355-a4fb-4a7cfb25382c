'use client'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from 'primereact/button'
import React, { useEffect, useState } from 'react'
import "../../../styles/styles.scss"
import { useEnvContext } from '../../../contexts/EnvContextProvider';
import { getOrgDataField } from '../../../utils/cookies';
import SummaryCard from './SummaryCard'
import BarChartComponent from './olk_chart'
import apiCall from '../../../utils/apiCallService'

interface DashboardData {
    olkstatus: number;
    "Total Sale": string;
    "Total Purchase": string;
    "pending receipts": string;
    "pending payments": string;
    "top 5 customers": {
        totalamount: string;
        custname: string;
    }[];
    "top 5 suppliers": {
        totalamount: string;
        custname: string;
    }[];
    "top selling items": {
        productdesc: string;
        ttlrevenue: string;
    }[];
}

type DashboardKey = keyof Pick<
    DashboardData,
    "Total Sale" | "Total Purchase" | "pending receipts" | "pending payments"
>;

const colorMap = {
    "Total Sale": "#188926",
    "Total Purchase": "#d8ab09",
    "pending receipts": "#bd3c2b",
    "pending payments": "#f55129",
};

const valueMap: { key: DashboardKey; label: string }[] = [
    { key: "Total Sale", label: "Total Sales" },
    { key: "Total Purchase", label: "Total Purchase" },
    { key: "pending receipts", label: "Pending Receipts" },
    { key: "pending payments", label: "Pending Payments" },
];

const OlkDashboard = () => {
    const [dashboardData, setDashboardData] = useState<DashboardData | null>(
        null
    );
    const [loading, setLoading] = useState(false);
    // const [showPendingList, setShowPendingList] = useState<
    //     "receipts" | "payments" | null
    // >(null);
    const router = useRouter();
    const { OLK_PATH } = useEnvContext();
    const orgCode = Number(getOrgDataField("orgcode"));

    useEffect(() => {
        const fetchDashboardData = async () => {
            setLoading(true);
            try {
                const response = await apiCall<DashboardData>("GET", `${OLK_PATH}/dashboard?orgcode=${orgCode}`);
                const data = response.data;
                setDashboardData(data);
            } catch (error) {
                console.log("error while fetching data: ", error);
            } finally {
                setLoading(false);
            }
        };
        fetchDashboardData();
    }, []);

    // const handlePendingClick = (type: "receipts" | "payments") => {
    //     setShowPendingList(type);
    // };

    // if (showPendingList) {
    //     return (
    //         <PartyPendingList
    //             type={showPendingList}
    //             onClose={() => setShowPendingList(null)}
    //         />
    //     );
    // }

    return loading ? (
        <i
            className="pi pi-spin pi-spinner mt-6 text-center"
            style={{ fontSize: "2rem" }}
        ></i>
    ) : (
        <div className="w-full card flex flex-column justify-content-center align-items-center gap-4">        
            {/* navigation buttons row  */}
            <div className="flex flex-wrap gap-4">
                 <Button
                    text
                    raised
                    label="Sales"
                    icon="pi pi-plus"
                    className="nav_button w-auto md:text-base icon-tight-gap"                    
                    onClick={() => router.push(
                            "/dashboard/invoice/sales-and-purchase?type=sales"
                        )
                    }
                />
                <Button
                    text
                    raised
                    label="Purchase"
                    icon="pi pi-plus"
                    className="nav_button w-auto md:text-base icon-tight-gap"                    
                    onClick={() =>
                        router.push(
                            "/dashboard/invoice/sales-and-purchase?type=purchase"
                        )
                    }
                />
                <Button
                    text
                    raised
                    label="Party"
                    icon="pi pi-plus"
                    className="nav_button w-auto md:text-base"
                    onClick={() => router.push("/dashboard/invoice/party")}
                />
                <Button
                    text
                    raised
                    label="Product/Service"
                    icon="pi pi-plus"
                    className="nav_button w-auto md:text-base icon-tight-gap"
                    onClick={() => router.push("/dashboard/invoice/product")
                    }
                />
               
            </div>

            {/* divider  */}
            <div className="custom-divider w-full md:max-w-screen"></div>
            <div className="grid">
                {valueMap.map((item, idx) => (
                    <div key={idx} className="col-6 lg:col-3 p-2">                        
                    <div
                            className="flex justify-between flex-column border-1 border-round-2xl p-3 align-items-center bg-white cursor-pointer s-btns h-full"
                            style={{
                                boxShadow: "5px 5px 0 #bfdbfe",
                                minWidth:
                                window.innerWidth >= 992 ? "200px" : "0",
                                width: "100%",

                            }}
                            onClick={() => {
                                if (item.key === "Total Sale") {
                                    router.push(
                                        `/dashboard/invoice/sales-and-purchase/list?title=${encodeURIComponent(
                                            "Details of Sales Invoice"
                                        )}`
                                    );
                                } else if (item.key === "Total Purchase") {
                                    router.push(
                                        `/dashboard/invoice/sales-and-purchase/list?title=${encodeURIComponent(
                                            "Details of Purchase Invoice"
                                        )}`
                                    );
                                } else if (item.key === "pending receipts") {
                                    router.push(
                                        `/dashboard/invoice/pending?type=receipts`
                                    );
                                } else if (item.key === "pending payments") {
                                    router.push(
                                        `/dashboard/invoice/pending?type=payments`
                                    );
                                }
                            }}
                        >
                            {/* Top Row: Title + Arrow */}
                            <div className="flex justify-between align-items-start w-full">
                                <span className="text-base font-bold text-left w-full whitespace-nowrap overflow-hidden text-ellipsis lg:whitespace-normal lg:overflow-visible lg:text-clip">
                                    {item.label}
                                </span>
                            </div>

                            {/* Bottom Row: Value */}
                            <div
                                className="text-sm lg:text-base lg:font-bold flex gap-2 align-items-center w-full justify-content-between mt-2" style={{ color: colorMap[item.key] }}>
                                <span className="whitespace-nowrap overflow-hidden text-ellipsis">                                   
                                ₹
                                    {Number(
                                        dashboardData?.[item.key] ?? 0
                                    ).toLocaleString("en-IN", {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2,
                                    })}
                                </span>
                                <i
                                    className="pi pi-angle-right flex-shrink-0"
                                    style={{ color: "#708090", fontSize: 16 }}
                                ></i>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {dashboardData && <BarChartComponent />}

            <div className="flex flex-wrap gap-4 justify-between w-full">
                <SummaryCard
                    title="Top Suppliers"
                    data={dashboardData?.["top 5 suppliers"]}
                />
                <SummaryCard
                    title="Top Customers"
                    data={dashboardData?.["top 5 customers"]}
                />
                <SummaryCard
                    title="Top Selling Products / Services"
                    data={dashboardData?.["top selling items"]}
                />
            </div>
        </div>
    );
};

export default OlkDashboard;
