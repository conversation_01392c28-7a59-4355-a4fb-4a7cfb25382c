"use client";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { <PERSON><PERSON> } from "primereact/button";
import axiosInstance from "../../../utils/axios";
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import { getOrgDataField } from "../../../utils/cookies";
import { AxiosError } from "axios";
// import PendingInvoicesTable from "./PendingInvoicesTable"; // Import the new component

interface PartyPendingData {
    partid: number;
    partyname: string;
    pendingamount: string;
}

interface PartyApiResponse {
    olkstatus: number;
    partywise_pending: PartyPendingData[];
}

interface PartyPendingListProps {
    type: "receipts" | "payments";
    onClose: () => void;
}

const PartyPendingList: React.FC<PartyPendingListProps> = ({
    type,
    onClose,
}) => {
    const [partyData, setPartyData] = useState<PartyPendingData[]>([]);
    // const [selectedParty, setSelectedParty] = useState<PartyPendingData | null>(
    //     null
    // );
    const [loading, setLoading] = useState(false);
    const router = useRouter();
    const { OLK_PATH } = useEnvContext();
    const orgCode = Number(getOrgDataField("orgcode"));

    const inoutflag = type === "receipts" ? 15 : 9;
    const title = type === "receipts" ? "Pending Receipts" : "Pending Payments";

    useEffect(() => {
        fetchPartyPendingData();
    }, [type]);

    const fetchPartyPendingData = async () => {
        setLoading(true);
        try {
            if (!OLK_PATH) {
                console.error("OLK_PATH is not defined in environment context");
                setPartyData([]);
                return;
            }

            const requestUrl = `${OLK_PATH}/invoice/ttlpartypending`;
            // console.log("Making request to:", requestUrl);

            const response = await axiosInstance.get(
                `/invoice/ttlpartypending`,
                {
                    baseURL: OLK_PATH,
                    params: {
                        orgcode: orgCode,
                        inoutflag: inoutflag,
                    },
                }
            );
            // console.log("API Response:", response.data);
            const data = response.data as PartyApiResponse;
            if (data.olkstatus === 0) {
                // Changed from 1 to 0 as 0 typically indicates success
                setPartyData(data.partywise_pending || []);
            } else {
                // console.error("API returned error status:", data.olkstatus);
                setPartyData([]);
            }
        } catch (error) {
            setPartyData([]);
        } finally {
            setLoading(false);
        }
    };

    const handlePartyClick = (party: PartyPendingData) => {
        router.push(
            `/dashboard/invoice/party/${party.partid}?type=${type}`
        );
    };

    const formatCurrency = (value: string) => {
        return `₹${Number(value).toLocaleString("en-IN", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        })}`;
    };

    const partyNameTemplate = (rowData: PartyPendingData) => {
        return (
            <div
                className="flex align-items-center cursor-pointer"
                onClick={() => handlePartyClick(rowData)}
            >
                <span className="font-bold">{rowData.partyname}</span>
                <i className="pi pi-angle-right ml-2 text-500"></i>
            </div>
        );
    };

    const pendingAmountTemplate = (rowData: PartyPendingData) => {
        return (
            <span className="text-red-500 font-semibold">
                {formatCurrency(rowData.pendingamount)}
            </span>
        );
    };

    return (
        <div className="card">
            <div className="flex justify-content-between align-items-center mb-4">
                <h2 className="text-2xl font-bold">{title}</h2>
                <Button icon="pi pi-times" text rounded onClick={onClose} />
            </div>

            {loading ? (
                <div className="flex justify-content-center">
                    <i
                        className="pi pi-spin pi-spinner"
                        style={{ fontSize: "2rem" }}
                    ></i>
                </div>
            ) : (
                <DataTable
                    value={partyData || []}
                    rows={50}
                    className="p-datatable-sm"
                    emptyMessage="No pending records found"
                    responsiveLayout="stack"
                    breakpoint="960px"
                    showGridlines
                    stripedRows
                    size="small"
                    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                >
                    <Column
                        field="partyname"
                        header="Party Name"
                        body={partyNameTemplate}
                        sortable
                        headerStyle={{
                            textAlign: "center",
                            backgroundColor: "#bfdbfe",
                        }}
                    />
                    <Column
                        field="pendingamount"
                        header="Pending Amount"
                        body={pendingAmountTemplate}
                        sortable
                        headerStyle={{
                            textAlign: "center",
                            backgroundColor: "#bfdbfe",
                        }}
                    />
                </DataTable>
            )}
        </div>
    );
};

export default PartyPendingList;
