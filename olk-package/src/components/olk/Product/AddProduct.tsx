"use client";
import { Card } from 'primereact/card';
import { InputText } from 'primereact/inputtext';
import { RadioButton } from 'primereact/radiobutton';
import { Button } from 'primereact/button';
import { Dropdown } from 'primereact/dropdown';
import { useState, useEffect } from 'react';
import { Toast } from 'primereact/toast';
import { useRef } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { getOrgDataField } from '../../../utils/cookies';
import { useEnvContext } from '../../../contexts/EnvContextProvider';   
import { Dialog } from 'primereact/dialog';
import { useTranslations } from 'next-intl';
import apiCall from '../../../utils/apiCallService';

const gst = [
  { label: "0% (nil-rated)", value: 0 },
  { label: "3%", value: 3 },
  { label: "5%", value: 5 },
  { label: "12%", value: 12 },
  { label: "18%", value: 18 },
  { label: "28%", value: 28 }
];

interface editProductProps {
  visible?: boolean;
  onSuccess?: () => void;
  onHide: () => void;
  product?: any;
  toastMsg: any
}

interface ApiResponse {
  olkstatus: number;
  olkresult: [];
}

const AddPSForm = ({ onHide, visible, onSuccess, product, toastMsg }: editProductProps) => {
  const t = useTranslations('AddPSForm');
  const [type, setType] = useState('Product');
  const toast = useRef<Toast>(null);
  const [loading, setLoading] = useState(false);
  const orgcode = Number(getOrgDataField('orgcode'));
  const { OLK_PATH } = useEnvContext();
  const [uomList, setUomList] = useState<{ label: string, value: string }[]>([]);
  const isEditMode = !!product;
  
  const defaultValues = {
    name: '',
    unit: '',
    mrp: '',
    salePrice: '',
    discountAmount: '',
    discountPercent: '',
    hsn: '',
    gst: 0,
  }

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues
  });

  useEffect(() => {
    apiCall<ApiResponse>("GET", `${OLK_PATH}/unitofmeasurement`)
      .then(response => {
        const units = response.data.olkresult.map((uom: any) => ({
          label: `${uom.unitname} - ${uom.description}`,
          value: uom.uomid,
        }));
        setUomList(units);
      })
      .catch(error => {
        console.error('Failed to fetch UOM list', error);
      });
  }, []);

  useEffect(() => {
    if (product) {
      setType(product.gsflag === 7 ? 'Product' : 'Service');
      reset({
        name: product.productdesc || '',
        unit: product.uomid || '',
        mrp: product.prodmrp?.toString() || '',
        salePrice: product.prodsp?.toString() || '',
        discountAmount: product.amountdiscount?.toString() || '',
        discountPercent: product.percentdiscount?.toString() || '',
        hsn: product.gscode || '',
        gst: product.tax?.IGST || '',
      });
    } else {
      reset(defaultValues);
      setType('Product');
    }
  }, [product, reset]);
  
    const onSubmit = async (data: any) => {
    setLoading(true);
    const productDetails: any = {
    productdesc: data.name,
    prodmrp: parseFloat(data.mrp || 0).toFixed(2),
    prodsp: parseFloat(data.salePrice || 0).toFixed(2),
    percentdiscount: parseFloat(data.discountPercent || 0).toFixed(2),
    amountdiscount: parseFloat(data.discountAmount || 0).toFixed(2) || '0.00',
  };

   if (data.gst !== "" && data.gst !== null && data.gst !== undefined) {
    productDetails.prodtax = {
      IGST: Number(data.gst),
    };
  }


  if (type === 'Product') {
    productDetails.uomid = data.unit;
  }
    const payload = {
      orgcode: orgcode,
      productdetails: productDetails,
      gsflag: type === 'Product' ? 7 : 19,
      gscode: data.hsn,
    };
    
    try {
      if (isEditMode) {
        await apiCall("PUT", `${OLK_PATH}/products`, {
          ...payload,
          productcode: product.productcode,
        });
        toastMsg.current.show({
          severity: 'success',
          summary: t('success'),
          detail: t('updateSuccess'),
          life: 3000,
        });
      } else {
        await apiCall("POST", `${OLK_PATH}/products`, payload)
        toast.current?.show({
          severity: 'success',
          summary: t('success'),
          detail: t('addSuccess'),
          life: 3000,
        });
      }
      onSuccess?.();
      reset(defaultValues);
      } catch (error: any) {
      const errorMessage = error?.response?.data?.message;

      // Check for specific error from API
      if (errorMessage === 'duplicateEntry') {
        toast.current?.show({
          severity: 'warn',
          summary: 'Warning',
          detail: 'This product already exists.',
          life: 3000,
        });
      } else {
        toast.current?.show({
          severity: 'error',
          summary: 'Error',
          detail: isEditMode ? 'Failed to update product' : 'Failed to add product',
          life: 3000,
        });
      }
    } finally {
      setLoading(false);
    }
  };
  
  const handleReset = () => {
    if (product) {
      setType(product.gsflag === 7 ? 'Product' : 'Service');
      reset({
        name: product.productdesc || '',
        unit: product.uomid || '',
        mrp: product.prodmrp?.toString() || '',
        salePrice: product.prodsp?.toString() || '',
        discountAmount: product.amountdiscount?.toString() || '',
        discountPercent: product.percentdiscount?.toString() || '',
        hsn: product.gscode || '',
        gst: product.tax?.IGST || '',
      });
    } else {
      reset(defaultValues);
      setType('Product');
    }
  };

  const footer = (
    <div className="form-footer flex gap-2 justify-end">
      <Button label={t('reset')} icon="pi pi-undo" className="p-button-secondary"
        onClick={handleReset}
      />
      <Button
        label={loading ? t('saving') : t('save')}
        icon="pi pi-save"
        onClick={handleSubmit(onSubmit)}
        disabled={loading}
        className='p-button-success'
      />
    </div>
  );

  const productForm = (
    <div className="max-w-3xl mx-auto">
      <Toast ref={toast} />
      <Card className="md:p-4 sm:p-2 border-1 border-gray-500" footer={footer}>
        {/* Role Selection */}
        <div className="formgroup-inline gap-4 mb-6">
          <h3 className="text-lg font-semibold mb-2">{t('itemType')}:</h3>
          <div className="flex gap-6">
            <div className="flex items-center">
              <RadioButton
                inputId="product"
                name="type"
                value="Product"
                onChange={(e) => setType(e.value)}
                checked={type === 'Product'}
                disabled={isEditMode}
              />
              <label htmlFor={isEditMode ? '' : "product"} className="ml-2">{t('product')}</label>
            </div>
            <div className="flex items-center">
              <RadioButton
                inputId="service"
                name="type"
                value="Service"
                onChange={(e) => setType(e.value)}
                checked={type === 'Service'}
                disabled={isEditMode}
              />
              <label htmlFor={isEditMode ? '' : "service"} className="ml-2">{t('service')}</label>
            </div>
          </div>
        </div>

        <div className="grid md:grid-cols-4 gap-4 mb-4 formgrid grid">
          
          <div className="field col-12 md:col-4">
            <Controller
              name="name"
              control={control}
              rules={{
                required: t('nameRequired'),
                maxLength: {
                  value: 100,
                  message: t('nameMaxLength'),
                },
              }}
              render={({ field }) => (
                <>
                  <label htmlFor="name">{t('name')} : <span className="text-red-500">*</span> </label>
                  <InputText id="name" {...field} className={`w-full ${errors.name ? 'p-invalid' : ''}`} />
                  {errors.name && <small className="p-error">{errors.name.message}</small>}
                </>
              )}
            />
          </div>

          {/* Unit of Measurement */}
          <div className="field col-12 md:col-4">
            <Controller
              name="unit"
              control={control}
              rules={{
                validate: (value) => {
                  if (type === 'Product' && !value) {
                    return t('uomRequired');
                  }
                  return true;
                },
              }}
              render={({ field }) => (
                <>
                  <label htmlFor="unit">{t('uom')} :{type === 'Product' && <span className="text-red-500"> *</span>} </label>
                  <Dropdown
                    {...field}
                    options={uomList}
                    optionLabel="label"
                    optionValue="value"
                    placeholder={t('selectUOM')}
                    filter
                    disabled={type === 'Service'}
                    showClear
                    className={`w-full ${errors.unit ? 'p-invalid' : ''} 
                    ${type === 'Service' ? 'bg-gray-200 disabled-cursor' : ''}`}
                  />
                  {errors.unit && <small className="p-error">{errors.unit.message}</small>}
                </>
              )}
            />
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-4 mb-4 formgrid grid">
          <div className="field col-12 md:col-4">
             <Controller
              name="hsn"
              control={control}
              rules={{
                validate: (value) => {
                  if (!value) return true; // Allow empty

                  const regex = /^(?!0+$)([0-9]{4}|[0-9]{6}|[0-9]{8})$/;
                  if (!regex.test(value)) {
                    return t('hsnValidation');
                  }

                  if (type === 'Product' && value.startsWith('99')) {
                    return t('hsnValid');
                  }

                  if (type !== 'Product' && !value.startsWith('99')) {
                    return t('sacValid');
                  }

                  return true;

                }
              }}
              render={({ field }) => (
                <>
                  <label className="form-label">{type === 'Product' ? "HSN :" : "SAC :"}</label>
                  <InputText {...field} className="w-full" keyfilter="pint" />
                  {errors.hsn && <p className="p-error">{errors.hsn.message}</p>}
                </>
              )}
            />
          </div>

          <div className="field col-12 md:col-4">
            <Controller
              name="gst"
              control={control}
              render={({ field }) => (
                <>
                  <label className="form-label">{t('gst')}:</label>
                  <Dropdown
                    {...field}
                    options={gst}
                    optionLabel="label"
                    optionValue="value"
                    placeholder={t('selectGST')}
                    className="w-full"
                  />
                </>
              )}
            />
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-4 mb-4 formgrid grid">
          <div className="field col-12 md:col-4 lg:col-4">
            <Controller
              name="mrp"
              control={control}
              rules={{
                validate: (value) => {
                  if (!value) return true
                  const regex = /^\d{1,10}(\.\d{1,2})?$/;
                  return regex.test(value) || "Up to 12 digits with 2 decimals allowed";
                }
              }}
              render={({ field }) => (
                <>
                  <label className="form-label">{t('mrp')} :</label>
                  <InputText {...field}
                    className="w-full"
                    keyfilter="money"
                    onChange={(e) => {
                      let value = e.target.value;

                      // Allow only digits and one optional decimal with up to 2 digits
                      const match = value.match(/^\d*\.?\d{0,2}/);
                      if (match) {
                        field.onChange(match[0]);
                      }
                    }}
                  // onBlur={(e) => {
                  //   const formatted = parseFloat(field.value || '0').toFixed(2);
                  //   field.onChange(formatted);
                  // }}
                  />
                  {errors.mrp && <p className="p-error">{errors.mrp.message}</p>}
                </>
              )}
            />
          </div>
          <div className="field col-12 md:col-4 lg:col-4">
            <Controller
              name="salePrice"
              defaultValue='salePrice'
              control={control}
              rules={{
                validate: (value) => {
                  if (!value) return true
                  const regex = /^\d{1,10}(\.\d{1,2})?$/;
                  return regex.test(value) || "Up to 12 digits with 2 decimals allowed";
                }
              }}
              render={({ field }) => (
                <>
                  <label className="form-label">{t('salePrice')} :</label>
                  <InputText {...field} className="w-full" keyfilter="money"
                    onChange={(e) => {
                      let value = e.target.value;

                      // Allow only digits and one optional decimal with up to 2 digits
                      const match = value.match(/^\d*\.?\d{0,2}/);
                      if (match) {
                        field.onChange(match[0]);
                      }
                    }}
                  />
                  {errors.salePrice && <p className="p-error">{errors.salePrice.message}</p>}
                </>
              )}
            />
          </div>

          <div className="field col-12 md:col-4 lg:col-4">
            <Controller
              name="discountAmount"
              control={control}
              rules={{
                validate: (value) => {
                  if (!value) return true
                  const regex = /^\d{1,10}(\.\d{1,2})?$/;
                  return regex.test(value) || "Up to 12 digits with 2 decimals allowed";
                }
              }}
              render={({ field }) => (
                <>
                  <label className="form-label">{t('discountAmount')} :</label>
                  <InputText {...field} className="w-full" keyfilter="money"
                    onChange={(e) => {
                      let value = e.target.value;

                      // Allow only digits and one optional decimal with up to 2 digits
                      const match = value.match(/^\d*\.?\d{0,2}/);
                      if (match) {
                        field.onChange(match[0]);
                      }
                    }}
                  />
                  {errors.discountAmount && <p className="p-error">{errors.discountAmount.message}</p>}
                </>
              )}
            />
          </div>

          <div className="field col-12 md:col-4 lg:col-4">
            <Controller
              name="discountPercent"
              control={control}
              rules={{
                validate: (value) => {
                  if (!value) return true
                  const regex = /^\d{1,10}(\.\d{1,2})?$/;
                  return regex.test(value) || "Up to 12 digits with 2 decimals allowed";
                }
              }}
              render={({ field }) => (
                <>
                  <label className="form-label">{t('discountPercent')} :</label>
                  <InputText {...field} className="w-full" keyfilter="money"
                    onChange={(e) => {
                      let value = e.target.value;

                      // Allow only digits and one optional decimal with up to 2 digits
                      const match = value.match(/^\d*\.?\d{0,2}/);
                      if (match) {
                        field.onChange(match[0]);
                      }
                    }}
                  />
                  {errors.discountPercent && <p className="p-error">{errors.discountPercent.message}</p>}
                </>
              )}
            />
          </div>
        </div>
      </Card>
    </div>
  )

  if (product) {
    return (
      <>
        <Dialog
          onHide={onHide}
          visible={visible}
          header={t('editProduct')}
          className="edit-modal text-primary"
          modal
        >
          {productForm}
        </Dialog>
      </>
    )
  } else {
    return (
      <div>
        {productForm}
      </div>
    )
  }
};

export default AddPSForm;