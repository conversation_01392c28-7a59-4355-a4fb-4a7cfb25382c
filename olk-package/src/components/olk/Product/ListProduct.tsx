"use client";
import { useCallback, useEffect, useRef, useState } from "react";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Button } from "primereact/button";
import { RadioButton } from "primereact/radiobutton";
import { Tag } from 'primereact/tag';
import { Dialog } from "primereact/dialog";
import "../../../styles/olkcss.scss"; 
import { getOrgDataField } from '../../../utils/cookies';
import { Divider } from "primereact/divider";
import { useEnvContext } from '../../../contexts/EnvContextProvider';   
import AddPSForm from "./AddProduct";
import { Toast } from "primereact/toast";
import { useTranslations } from 'next-intl';
import apiCall from "../../../utils/apiCallService";

interface ApiResponse {
  olkstatus: number;
  olkresult: [];
}

const ProdListTable = () => {
  const t = useTranslations('ProdListTable');
  const [products, setProducts] = useState<any[]>([]);
  const [filterType, setFilterType] = useState("Both");
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState<string | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<any | null>(null);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [addProductVisible, setAddProductVisible] = useState(false);
  const [productToEdit, setProductToEdit] = useState<any>(null);
  const { OLK_PATH } = useEnvContext();
  const toast = useRef<Toast>(null);

  useEffect(() => {
    const mediaQuery = window.matchMedia("(max-width: 767px)");

    const handleChange = (e: any) => {
      if (e.matches) {
        setFilterType("Product");
      } else {
        setFilterType("Both");
      }
    };

    // Initial check
    handleChange(mediaQuery);

    // Add listener
    mediaQuery.addEventListener("change", handleChange);

    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    setLoading(true);
    const orgcode = Number(getOrgDataField('orgcode'));
    if (!orgcode) {
      setMessage(t('orgNotFound'));
      setProducts([]);
      setLoading(false);
      return;
    }

    try {
      const response = await apiCall<ApiResponse>("GET", `${OLK_PATH}/products?orgcode=${orgcode}`);
      const { olkstatus, olkresult } = response.data;

      if (olkstatus !== 0) {
        setMessage(t('productsNotFound'));
        setProducts([]);
      } else if (olkresult.length === 0) {
        setMessage(t('noProducts'));
        setProducts([]);
      } else {
        const mapped = olkresult.map((item: any) => ({
          name: item.productdesc,
          type: item.gsflag === 7 ? t('product') : t('service'),
          hsn: item.gscode || "-",
          gst: `${item.tax?.IGST || 0}%`,
          mrp: item.prodmrp,
          sp: item.prodsp,
          deletable: item.deletable,
          fullData: item,
        }));
        setProducts(mapped);
        setMessage(null);
      }
    } catch (error) {
      setMessage(t('fetchError'));
      setProducts([]);
    }
    setLoading(false);
  };

  const filteredProds = useCallback(() => {
    return products.filter((prod) => {
      if (filterType === "Both") return true;
      return prod.type === filterType;
    });
  }, [products, filterType])

  const typeTagTemplate = (rowData: any) => {
    const type = rowData.type;
    const isProduct = type === t('product');
    return (
      <Tag
        value={type}
        severity={isProduct ? "info" : "success"}
        className="text-sm px-3 py-1"
        style={{ borderRadius: '10px' }}
      />
    );
  };

  const handleEdit = (productData: any) => {
    setAddProductVisible(true);
    setProductToEdit(productData);
  };

  const actionTemplate = (rowData: any) => (
    <div className="flex items-center gap-2">
      <Button
        icon="pi pi-eye"
        className="p-button-sm p-button-text p-button-info"
        onClick={() => {
          setSelectedProduct(rowData.fullData);
          setDialogVisible(true);
        }}
        tooltip={t('view')}
        tooltipOptions={{ position: 'top' }}
      />
      <Button
        icon="pi pi-pencil"
        className="p-button-sm p-button-text ml-2"
        onClick={() => handleEdit(rowData.fullData)}
        tooltip={t('edit')}
        tooltipOptions={{ position: 'top' }}
      />
      {rowData.deletable && (
        <Button
          icon="pi pi-trash"
          className="p-button-sm p-button-text p-button-danger"
          onClick={() => {
            // You can hook delete logic here
            alert(t('deleteConfirm', { name: rowData.name }));
          }}
          tooltip={t('delete')}
          tooltipOptions={{ position: 'top' }}
        />
      )}
    </div>
  );

  return (
    <div>
      <Toast ref={toast} />
      {/* Filter Controls */}
      <div className="mb-4 flex gap-4 items-center">
         <span className="hidden md:block">{t('show')}:</span>
        <div className="md:flex items-center gap-2 hidden">
          <RadioButton 
            inputId="both" 
            name="prodyType" 
            value="Both" 
            onChange={(e) => setFilterType(e.value)} 
            checked={filterType === "Both"} 
          />
          <label htmlFor="both">{t('both')}</label>
        </div>
        <div className="flex items-center gap-2">
          <RadioButton 
            inputId="product" 
            name="prodyType" 
            value={t('product')} 
            onChange={(e) => setFilterType(e.value)} 
            checked={filterType === t('product')} 
          />
          <label htmlFor="product">{t('product')}</label>
        </div>
        <div className="flex items-center gap-2">
          <RadioButton 
            inputId="service" 
            name="prodyType" 
            value={t('service')} 
            onChange={(e) => setFilterType(e.value)} 
            checked={filterType === t('service')} 
          />
          <label htmlFor="service">{t('service')}</label>
        </div>
      </div>

    {message ? (
        <div className="text-red-600">{message}</div>
      ) : (
        <>
          <div className="scroll-wrapper hidden md:block">
            <DataTable value={filteredProds()} loading={loading} rows={20} scrollable scrollHeight="700px" className="min-w-[600px]">
              <Column field="name" header="Name" sortable style={{ width: "25%" }} />
              <Column header="Type" body={typeTagTemplate} />
              <Column field="hsn" header={t('hsn')} />
              <Column field="gst" header={t('gst')} />
              <Column field="mrp" header={t('mrp')}/>
              <Column field="sp" header={t('salePrice')} />
              <Column body={actionTemplate} header="Actions" style={{ width: "8rem" }} />
            </DataTable>
          </div>

          <div className="flex flex-column gap-4 md:hidden">
            {filteredProds().map((item, idx) => (
              <div key={idx} className="border border-1 p-2 border-round-xl flex justify-content-between" 
              style={{ borderColor: '#6366f1' }}>

                <div className="flex flex-column flex-wrap gap-2 mt-3 mb-3">
                  <div>
                    <span className="p-colour text-xl font-semibold">{item.name}</span>
                    <span className="v-colour"> {item.hsn === '-' ? '' : `(${item.hsn})`}</span>
                  </div>
                  <span className="min-w-0 break-words">
                    <span className="font-semibold" style={{fontSize:'1rem'}}>GSTIN : </span>
                    <span className="sm-col">{item.gst}</span>
                  </span>
                  <span className="min-w-0 break-words">
                    <span className="font-semibold" style={{fontSize:'1rem'}}>MRP : </span>
                    <span className="sm-col">{item.mrp}</span>
                  </span>
                  <span className="min-w-full sm:min-w-[33%] break-words">
                    <span className="font-semibold" style={{fontSize:'1rem'}}>Sale Price : </span>
                    <span className="sm-col">{item.sp}</span>
                  </span>
                </div>

                <div className="flex flex-column align-items-center mt-2">
                  <Button
                    icon="pi pi-eye"
                    className="border-0 h-2 p-2 w-auto p-colour m-0 outline-none shadow-none"
                    style={{backgroundColor:'white',margin:'0 !important'}}
                    onClick={() => {
                      setSelectedProduct(item.fullData);
                      setDialogVisible(true);
                    }}
                  />
                  <Button
                    icon="pi pi-pencil"
                    className="border-0 h-2 p-2 w-auto p-colour m-0 outline-none shadow-none"
                    style={{backgroundColor:'white',margin:'0 !important'}}
                    onClick={() => handleEdit(item.fullData)} // Pass the party data to handleEdit
                  />
                  {item.deletable && (
                    <Button
                      icon="pi pi-trash"
                      className="border-0 h-2 p-2 w-auto m-0 outline-none shadow-none"
                      style={{backgroundColor:'white',color:"red",margin:'0 !important'}}
                      onClick={() => {
                        // You can hook delete logic here
                        alert(`Deleting ${item.name}`);
                      }}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        </>
      )}

      <Dialog
        header={t('productDetails')}
        visible={dialogVisible}
        onHide={() => setDialogVisible(false)}
        modal
        className="p-fluid"
        breakpoints={{ '960px': '95vw', '640px': '100vw' }}
        style={{ width: '75vw' }}>
        {selectedProduct && (

          <div className="p-3">
            <div className="flex flex-column sm:flex-row sm:justify-content-between sm:align-items-center mb-2">
              <h3 className="text-xl text-primary font-bold m-0">{selectedProduct.productdesc}</h3>
              <Tag
                value={selectedProduct.gsflag === 7 ? t('product') : t('service')}
                severity={selectedProduct.gsflag === 7 ? "info" : "success"}
                className="mt-2 sm:mt-0 text-sm sm:text-base px-3 py-2"
                style={{ borderRadius: '12px' }}
              />
            </div>
            <Divider layout="horizontal" className="my-2" />
            {/* Row 1: Name, Type, UOM */}
            <div className="grid formgrid mb-2">
              <div className="field col-12 md:col-6">
                <strong>{t('uom')} :</strong> {selectedProduct.uom_name}
              </div>
            </div>

            {/* Row 2: HSN, GST */}
            <div className="grid formgrid mb-2">
              <div className="field col-12 md:col-6">
                <strong>HSN :</strong> {selectedProduct.gscode || "-"}
              </div>
              <div className="field col-12 md:col-6">
                <strong>GST :</strong> {selectedProduct.tax?.IGST || 0}%
              </div>
            </div>

            {/* Row 3: MRP, Selling Price */}
            <div className="grid formgrid mb-2">
              <div className="field col-12 md:col-6">
                <strong>MRP :</strong> ₹{selectedProduct.prodmrp}
              </div>
              <div className="field col-12 md:col-6">
                <strong>Sale Price :</strong> ₹{selectedProduct.prodsp}
              </div>
            </div>

            {/* Row 4: Discount Amount, Discount % */}
            <div className="grid formgrid">
              <div className="field col-12 md:col-6">
                <strong>Discount Amount :</strong> ₹{selectedProduct.amountdiscount}
              </div>
              <div className="field col-12 md:col-6">
                <strong>Discount % :</strong> {selectedProduct.percentdiscount}%
              </div>
            </div>
          </div>
        )}
      </Dialog>

      {productToEdit && (
        <>
          {/* if no product are there to edit, so normal form mode */}
          {!productToEdit && <AddPSForm
            visible={true}
            onSuccess={() => {
              fetchProducts();
              setAddProductVisible(false);
              setProductToEdit(null);
            }}
            onHide={() => {
              setAddProductVisible(false);
              setProductToEdit(null);
            }}
            product={productToEdit}
            toastMsg={toast}
          />}

          {/* edit mode form */}
          <AddPSForm
            visible={!!productToEdit}
            onSuccess={() => {
              fetchProducts();
              setAddProductVisible(false);
              setProductToEdit(null);
            }}
            onHide={() => {
              setAddProductVisible(false);
              setProductToEdit(null);
            }}
            product={productToEdit}
            toastMsg={toast}
          />
        </>
      )

      }
    </div>
  );
};

export default ProdListTable;
