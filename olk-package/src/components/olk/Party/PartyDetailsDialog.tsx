import { Dialog } from "primereact/dialog";
import { Tag } from 'primereact/tag';
import { Divider } from "primereact/divider";
import { useTranslations } from 'next-intl';

interface PartyDetailsDialogProps {
  visible: boolean;
  onHide: () => void;
  party: any | null;
}

const PartyDetailsDialog = ({ visible, onHide, party }: PartyDetailsDialogProps) => {
  const t = useTranslations('PartyDetailsDialog');
  
  if (!party) return null;

  const getPartyType = () => {
    return party.csflag === 3 ? t('partyTypes.customer') : t('partyTypes.supplier');
  };

  const getSeverity = () => {
    return party.csflag === 3 ? "success" : "info";
  };

  return (
    <Dialog
      className="text-primary"
      header={t('header')}
      visible={visible}
      style={{ width: '70vw' }}
      breakpoints={{ '960px': '90vw', '640px': '95vw' }}
      onHide={onHide}
      modal
    >
      <div className="p-4 text-base">

        {/* Header Row */}
        <div className="grid align-items-center mb-3">
          <div className="col-12 md:col-8">
            <h3 className="text-xl sm:text-2xl font-bold text-primary m-0">{party.custname}</h3>
          </div>
          <div className="col-12 md:col-4 text-right">
            <Tag
              value={getPartyType()}
              severity={getSeverity()}
              className="mt-2 sm:mt-0 text-sm sm:text-base px-3 py-2"
              style={{ borderRadius: '12px' }}
            />
          </div>
        </div>

        <Divider className="my-2" />

        {/* Basic Details */}
        <div className="grid">
          {[
            { label: t('labels.contact'), value: party.custphone },
            { label: t('labels.email'), value: party.custemail },
            { label: t('labels.address'), value: party.custaddr },
            { label: t('labels.state'), value: party.state },
            { label: t('labels.pincode'), value: party.pincode },
            { label: t('labels.gstin'), value: Object.values(party.gstin || {})[0] || t('labels.na') },
            { label: t('labels.pan'), value: party.custpan || t('labels.na') }
          ].map((item, idx) => (
            <div key={idx} className="col-12 md:col-6 mb-3">
              <div className="flex justify-between gap-4">
                <span className="text-color-secondary font-semibold">{item.label}:</span>
                <span className="font-medium text-right break-words">{item.value}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Bank Details */}
        <div className="mt-4">
          <div className="p-3 surface-card border-round shadow-1">
            <h5 className="mb-3 text-lg text-primary">{t('sections.bankDetails')}</h5>
            <div className="grid">
              {[
                { label: t('labels.accName'), value: party.bankdetails?.accountname || t('labels.dash') },
                { label: t('labels.accNo'), value: party.bankdetails?.accountno || t('labels.dash') },
                { label: t('labels.ifsc'), value: party.bankdetails?.ifsc || t('labels.dash') },
                { label: t('labels.branch'), value: party.bankdetails?.branchname || t('labels.dash') }
              ].map((item, idx) => (
                <div key={idx} className="col-12 md:col-6 mb-2">
                  <div className="flex justify-between gap-4">
                    <span className="text-color-secondary font-semibold">{item.label}:</span>
                    <span className="font-medium text-right break-words">{item.value}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
};

export default PartyDetailsDialog;