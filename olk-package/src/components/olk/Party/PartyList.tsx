"use client";
import { useCallback, useEffect, useState,useRef } from "react";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Button } from "primereact/button";
import { RadioButton } from "primereact/radiobutton";
import { Toast } from "primereact/toast";
import { Tag } from 'primereact/tag';
import { getOrgDataField } from '../../../utils/cookies';
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import { useTranslations } from 'next-intl';
import "../../../styles/olkcss.scss"; 
import PartyDetailsDialog from "./PartyDetailsDialog";
import AddPartyForm from "./AddParty";
import apiCall from "../../../utils/apiCallService";

interface ApiResponse {
  olkstatus: number;
  olkresult: [];
}

const PartyListTable = () => {
  const t = useTranslations('PartyListTable');
  const [parties, setParties] = useState<any[]>([]);
  const [filterType, setFilterType] = useState("Both");
  const [loading, setLoading] = useState(false);
  const [selectedParty, setSelectedParty] = useState<any>(null);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [addPartyVisible, setAddPartyVisible] = useState(false); // State for AddParty modal
  const [partyToEdit, setPartyToEdit] = useState<any>(null); // State for the party being edited
  const toast = useRef(null);
  const { OLK_PATH } = useEnvContext();

  useEffect(() => {
    const mediaQuery = window.matchMedia("(max-width: 767px)");

    const handleChange = (e: any) => {
      if (e.matches) {
        setFilterType("Customer");
      } else {
        setFilterType("Both");
      }
    };

    // Initial check
    handleChange(mediaQuery);

    // Add listener
    mediaQuery.addEventListener("change", handleChange);

    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  useEffect(() => {
    fetchParties();
  }, []);

  const fetchParties = async () => {
    const orgcode = Number(getOrgDataField('orgcode'));
    if (!orgcode) return;

    setLoading(true);
    try {
      const res =  await apiCall<ApiResponse>("GET", `${OLK_PATH}/parties?orgcode=${orgcode}`);
      console.log(res.data);
      const { olkstatus, olkresult } = res.data;
      if (olkstatus === 1) {
        (toast.current as any)?.show({ severity: "error",  summary: t('errors.error'), 
          detail: t('errors.fetchFailed'), life: 3000 });
        setParties([]);
      } else if (olkresult.length === 0) {
        (toast.current as any)?.show({ severity: "info",  summary: t('messages.info'), 
          detail: t('messages.noParties'), life: 3000 });
        setParties([]);
      } else {
        // Map API fields to UI-friendly structure
        // console.log(olkresult[0].gstin);
        const mappedParties = olkresult.map((item: any) => ({
          id: item.custid,
          name: item.custname,
          contact: item.custphone,
          type: item.csflag === 3 ? "Customer" : item.csflag === 19 ? "Supplier" : "Unknown",
          displayType: item.csflag === 3 ? t("partyTypes.customer") : item.csflag === 19 ? t("partyTypes.supplier") : t("partyTypes.unknown"),
          state: item.state,
          gstin: item?.gstin ? Object.values(item.gstin)[0] || '' : '',
          deletable: item.deletable,
          fullData: item,
        }));

        setParties(mappedParties);
      }
    } catch (err) {
      (toast.current as any)?.show({ severity: "error", summary: "Error", detail: "API error occurred", life: 3000 });
    }
    setLoading(false);
  };

  const filteredParties = useCallback(() => {
    return parties.filter((party) => {
      if (filterType === "Both") return true;
      return party.type === filterType;
    });
  }, [filterType, parties])

  const handleEdit = (party: any) => {
    setPartyToEdit(party); // Set the party to edit
    setAddPartyVisible(true); // Show the AddParty modal
  };

  const handlePartyUpdated = () => {
    fetchParties(); // Refresh the list after edit
  };

  const handleView = (rowData: any) => {
    const data = rowData.fullData;
    (toast.current as any)?.show({ severity: "info", summary: "Party Info", detail: `Viewing ${data.custname}`, life: 2000 });
  };

  
  const typeTagTemplate = (rowData: any) => {
    const type = rowData.type;
    const isCust = rowData.type === "Customer";
    return (
      <Tag
        value={rowData.displayType}
        severity={isCust ? "success" : "info"}
        className="text-sm px-3 py-1"
        style={{ borderRadius: '10px' }}
      />
    );
  };


  const actionTemplate = (rowData: any) => (
    <div className="flex items-center gap-2">
      <Button
        icon="pi pi-eye"
        className="p-button-sm p-button-text"
        onClick={() => {
          setSelectedParty(rowData.fullData);
          setDialogVisible(true);
        }}
      />
      <Button
        icon="pi pi-pencil"
        className="p-button-sm p-button-text ml-2"
        onClick={() => handleEdit(rowData.fullData)} // Pass the party data to handleEdit
      />
      {rowData.fullData?.deletable && (
        <Button
          icon="pi pi-trash"
          className="p-button-sm p-button-text p-button-danger ml-2"
        />
      )}
    </div>
  );

  return (
    <div>
      <Toast ref={toast} />

      {/* Filter Controls */}
      <div className="mb-4 flex gap-4 items-center">
        {/* Show "Both" radio only on desktop */}
        <span className="hidden md:block">Show:</span>
        <div className="hidden md:flex items-center gap-2">
          <RadioButton
            inputId="both"
            name="partyType"
            value="Both"
            onChange={(e) => setFilterType(e.value)}
            checked={filterType === "Both"}
          />
          <label htmlFor="both">Both</label>
        </div>
        {["Customer", "Supplier"].map((type) => (
          <div key={type} className="flex items-center gap-2">
            <RadioButton
              inputId={type.toLowerCase()}
              name="partyType"
              value={type}
              onChange={(e) => setFilterType(e.value)}
              checked={filterType === type}
            />
            <label htmlFor={type.toLowerCase()}>{t(`partyTypes.${type.toLowerCase()}`)}</label>
          </div>
        ))}
      </div>

      {/* Table */}
      <div className="scroll-wrapper hidden md:block">
        <DataTable value={filteredParties()} rows={20} scrollable scrollHeight="700px" className="min-w-[600px]" loading={loading}>
          <Column field="name" header={t('headers.name')} sortable style={{ width: '20%' }} />
          <Column field="contact" header={t('headers.contact')} />
          <Column header={t('headers.type')} body={typeTagTemplate} />
          <Column field="state" header={t('headers.state')} />
          <Column field="gstin" header={t('headers.gstin')} />
          <Column body={actionTemplate} header={t('headers.actions')} style={{ width: "10rem" }} />
        </DataTable>
      </div>

      <div className="flex flex-column gap-4 md:hidden">
        {filteredParties().map((item, idx) => (
          <div key={idx} className="border border-1 p-2 border-round-xl flex justify-content-between"
            style={{ borderColor: '#6366f1' }}>

            <div className="flex flex-column flex-wrap gap-2 mt-3 mb-3">
              <span className="p-colour text-xl font-semibold">{item.name}</span>
              <span className="min-w-0 break-words">
                <span className="font-semibold" style={{ fontSize: '1rem' }}>{t('headers.contact')} : </span>
                <span className="sm-col">{item.contact}</span>
              </span>
              <span className="min-w-0 break-words">
                <span className="font-semibold" style={{ fontSize: '1rem' }}>GSTIN : </span>
                <span className="sm-col">{item.gstin}</span>
              </span>
            </div>

            <div className="flex flex-column items-center align-items-center">
              <Button
                icon="pi pi-eye"
                className="border-0 h-2 p-1 w-auto p-colour m-0 outline-none shadow-none"
                style={{ backgroundColor: 'white', margin: '0 !important' }}
                onClick={() => {
                  setSelectedParty(item.fullData);
                  setDialogVisible(true);
                }}
              />
              <Button
                icon="pi pi-pencil"
                className="border-0 h-2 p-1 w-auto p-colour m-0 outline-none shadow-none"
                style={{ backgroundColor: 'white', margin: '0 !important' }}

                onClick={() => handleEdit(item.fullData)} // Pass the party data to handleEdit
              />
              {item.fullData?.deletable && (
                <Button
                  icon="pi pi-trash"
                  className="border-0 h-2 p-1 w-auto p-colour m-0 outline-none shadow-none"
                  style={{ backgroundColor: 'white', margin: '0 !important',color:'red' }}
                />
              )}
            </div>
          </div>
        ))}
      </div>

      <PartyDetailsDialog
        visible={dialogVisible}
        onHide={() => setDialogVisible(false)}
        party={selectedParty}
      />

      {addPartyVisible && (
        <>
          {/* Normal form for adding */}
          {!partyToEdit && (
            <AddPartyForm
              visible={true}
              onSuccess={() => {
                fetchParties();
                setAddPartyVisible(false);
              }}
              onHide={() => setAddPartyVisible(false)}
            />
          )}

          {/* Dialog for editing */}
          <AddPartyForm
            visible={!!partyToEdit}
            party={partyToEdit}
            onSuccess={() => {
              fetchParties();
              setAddPartyVisible(false);
              setPartyToEdit(null);
            }}
            onHide={() => {
              setAddPartyVisible(false);
              setPartyToEdit(null);
            }}
          />
        </>
      )}
    </div>
  );
};

export default PartyListTable;
