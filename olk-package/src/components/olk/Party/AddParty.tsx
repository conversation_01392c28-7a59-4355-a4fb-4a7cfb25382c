"use client";

import React, { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Toast } from "primereact/toast";
import { Button } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import { RadioButton } from "primereact/radiobutton";
import { Divider } from "primereact/divider";
import { classNames } from "primereact/utils";
import { Card } from "primereact/card";
import { getOrgDataField } from '../../../utils/cookies';
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import { Dialog } from "primereact/dialog";
import { useTranslations } from 'next-intl';
import apiCall from "../../../utils/apiCallService";

interface StateOption {
  label: string;
  value: string;
  statecode: string;
}

interface AddPartyFormProps {
  visible?: boolean;
  party?: any;
  onSuccess?: () => void;
  onHide: () => void;
}

interface StateResponse {
  olkstatus: number;
  olkresult: StateOption[];
}

export default function AddPartyForm({ visible, party, onSuccess, onHide }: AddPartyFormProps) {
  const t = useTranslations('AddPartyForm');
  const st = useTranslations('ReceiverDetails');
  const [role, setRole] = useState("Customer");
  const [loading, setLoading] = useState(false);
  const [stList, setStList] = useState<StateOption[]>([]);
  const toast = useRef<Toast>(null);
  const orgcode = Number(getOrgDataField('orgcode'));
  const isEditMode = !!party;

  const defaultValues = {
    name: "",
    email: "",
    contact: "",
    address: "",
    state: "",
    pincode: "",
    pan: "",
    gstin: "",
    accNo: "",
    ifsc: "",
    branch: "",
    accName: "",
  };

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm({ defaultValues });
  const { OLK_PATH } = useEnvContext();

  const states: StateOption[] =[
  { label: st('states.Jammu and Kashmir'), value: 'Jammu and Kashmir', statecode: 'JK' },
  { label: st('states.Himachal Pradesh'), value: 'Himachal Pradesh', statecode: 'HP' },
  { label: st('states.Punjab'), value: 'Punjab', statecode: 'PB' },
  { label: st('states.Chandigarh'), value: 'Chandigarh', statecode: 'CH' },
  { label: st('states.Uttarakhand'), value: 'Uttarakhand', statecode: 'UK' },
  { label: st('states.Haryana'), value: 'Haryana', statecode: 'HR' },
  { label: st('states.Delhi'), value: 'Delhi', statecode: 'DL' },
  { label: st('states.Rajasthan'), value: 'Rajasthan', statecode: 'RJ' },
  { label: st('states.Uttar Pradesh'), value: 'Uttar Pradesh', statecode: 'UP' },
  { label: st('states.Bihar'), value: 'Bihar', statecode: 'BR' },
  { label: st('states.Sikkim'), value: 'Sikkim', statecode: 'SK' },
  { label: st('states.Arunachal Pradesh'), value: 'Arunachal Pradesh', statecode: 'AR' },
  { label: st('states.Nagaland'), value: 'Nagaland', statecode: 'NL' },
  { label: st('states.Manipur'), value: 'Manipur', statecode: 'MN' },
  { label: st('states.Mizoram'), value: 'Mizoram', statecode: 'MZ' },
  { label: st('states.Tripura'), value: 'Tripura', statecode: 'TR' },
  { label: st('states.Meghalaya'), value: 'Meghalaya', statecode: 'ML' },
  { label: st('states.Assam'), value: 'Assam', statecode: 'AS' },
  { label: st('states.West Bengal'), value: 'West Bengal', statecode: 'WB' },
  { label: st('states.Jharkhand'), value: 'Jharkhand', statecode: 'JH' },
  { label: st('states.Odisha'), value: 'Odisha', statecode: 'OD' },
  { label: st('states.Chhattisgarh'), value: 'Chhattisgarh', statecode: 'CG' },
  { label: st('states.Madhya Pradesh'), value: 'Madhya Pradesh', statecode: 'MP' },
  { label: st('states.Gujarat'), value: 'Gujarat', statecode: 'GJ' },
  { label: st('states.Daman and Diu'), value: 'Daman and Diu', statecode: 'DD' },
  { label: st('states.Dadra and Nagar Haveli'), value: 'Dadra and Nagar Haveli', statecode: 'DNH' },
  { label: st('states.Maharashtra'), value: 'Maharashtra', statecode: 'MH' },
  { label: st('states.Andhra Pradesh'), value: 'Andhra Pradesh', statecode: 'AD' },
  { label: st('states.Karnataka'), value: 'Karnataka', statecode: 'KA' },
  { label: st('states.Goa'), value: 'Goa', statecode: 'GA' },
  { label: st('states.Lakshdweep'), value: 'Lakshdweep', statecode: 'LD' },
  { label: st('states.Kerala'), value: 'Kerala', statecode: 'KL' },
  { label: st('states.Tamil Nadu'), value: 'Tamil Nadu', statecode: 'TN' },
  { label: st('states.Pondicherry'), value: 'Pondicherry', statecode: 'PY' },
  { label: st('states.Andaman and Nicobar Islands'), value: 'Andaman and Nicobar Islands', statecode: 'AN' },
  { label: st('states.Telangana'), value: 'Telangana', statecode: 'TS' },
  { label: st('states.Andhra Pradesh (New)'), value: 'Andhra Pradesh (New)', statecode: 'AD' }
];



  // const onSubmit = async (data: any) => {
  //   setLoading(true);
  //   const selectedState = stList.find((s) => s.value === data.state);


    const onSubmit = async (data: any) => {
    setLoading(true);
    const selectedState = states.find((s) => s.value === data.state);

    if (!selectedState) {
      toast.current?.show({
        severity: 'warn',
        summary: t('validation.summary'),
        detail: t('validation.selectValidState'),
      });
      setLoading(false);
      return;
    }


    // if (!selectedState) {
    //   toast.current?.show({
    //     severity: "warn",
    //     summary: t('validation.summary'),
    //     detail: t('validation.selectValidState'),
    //   });
    //   setLoading(false);
    //   return;
    // }

    const basePayload: any = {
      orgcode: Number(orgcode),
    };

    basePayload.custname = data.name || "";
    basePayload.custemail = data.email || "";
    basePayload.custphone = data.contact || "";
    basePayload.custaddr = data.address || "";
    basePayload.custstate = data.state || "";
    basePayload.pincode = data.pincode || "";
    basePayload.custpan = data.pan || "";

    if (!isEditMode) {
      basePayload.csflag = role === "Customer" ? 3 : 19;
    }

    // Handle GSTIN if provided
    // if (data.gstin && selectedState) {
    //   basePayload.gstin = {
    //     [selectedState.statecode]: data.gstin
    //   };
    // }

    if (isEditMode) {
    // In edit mode, always include gstin (empty object if cleared)
    basePayload.gstin = data.gstin && selectedState ? { [selectedState.statecode]: data.gstin } : {};
  } else {
    // In add mode, include gstin only if provided
    if (data.gstin && selectedState) {
      basePayload.gstin = { [selectedState.statecode]: data.gstin };
    }
  }

    if (isEditMode) {
    // In edit mode, always include bankdetails (with all fields, even if empty)
    basePayload.bankdetails = {
      accountno: data.accNo || "",
      ifsc: data.ifsc || "",
      branchname: data.branch || "",
      accountname: data.accName || "",
    };
  } else {
    // In add mode, include bankdetails only if at least one field is provided
    if (data.accNo || data.ifsc || data.branch || data.accName) {
      basePayload.bankdetails = {
        accountno: data.accNo || "",
        ifsc: data.ifsc || "",
        branchname: data.branch || "",
        accountname: data.accName || "",
      };
    }
  }

    const finalPayload = isEditMode
      ? { ...basePayload, customer_id: party?.custid }
      : basePayload;

    try {
      if (isEditMode) {
      console.log("Final Payload",finalPayload);

        await apiCall("PUT", `${OLK_PATH}/parties`, finalPayload);
      } else {
        await apiCall("POST", `${OLK_PATH}/parties`, finalPayload);
      }

      toast.current?.show({
        severity: "success",
        summary: t('success.summary'),
        detail: isEditMode ? t('success.partyEdited') : t('success.partyAdded'),
      });

      reset(defaultValues);
      setRole("Customer");
      if (onSuccess) onSuccess();
    } catch (error) {
      console.error(error);
      toast.current?.show({
        severity: "error",
        summary: t('errors.summary'),
        detail: isEditMode ? t('errors.failedToUpdate') : t('errors.failedToSave'),
      });
    } finally {
      setLoading(false);
    }
  };

  // useEffect(() => {
  //      apiCall<StateResponse>("GET", `${OLK_PATH}/state`)
  //     .then((res) => {
  //       const list = res.data.olkresult.map((state: any) => ({
  //         label: state.statename,
  //         value: state.statename,
  //         statecode: state.statecode,
  //       }));
  //       setStList(list);
  //     })
  //     .catch((error) => {
  //       console.error("Error fetching states:", error);
  //     });
  // }, []);

  const handleReset = () => {
    if (party) {
      // Reset to original values
      const gstinCode = Object.keys(party.gstin || {})[0];
      const gstinVal = party.gstin?.[gstinCode] || "";
      reset({
        name: party.custname || "",
        email: party.custemail || "",
        contact: party.custphone || "",
        address: party.custaddr || "",
        state: party.custstate || "",
        pincode: party.pincode || "",
        pan: party.custpan || "",
        gstin: gstinVal,
        accNo: party.bankdetails?.accountno || "",
        ifsc: party.bankdetails?.ifsc || "",
        branch: party.bankdetails?.branchname || "",
        accName: party.bankdetails?.accountname || "",
      });
    } else {
      reset(defaultValues);
    }
  }

  useEffect(() => {
    if (party) {
      // Populate form if editing
      const gstinCode = Object.keys(party.gstin || {})[0];
      const gstinVal = party.gstin?.[gstinCode] || "";
      reset({
        name: party.custname || "",
        email: party.custemail || "",
        contact: party.custphone || "",
        address: party.custaddr || "",
        state: party.state || "",
        pincode: party.pincode || "",
        pan: party.custpan || "",
        gstin: gstinVal,
        accNo: party.bankdetails?.accountno || "",
        ifsc: party.bankdetails?.ifsc || "",
        branch: party.bankdetails?.branchname || "",
        accName: party.bankdetails?.accountname || "",
      });
      setRole(party.csflag === 3 ? "Customer" : "Supplier");
    } else {
      reset(defaultValues);
      setRole("Customer");
    }
  }, [party, reset]);

  const footer = (
    <div className="form-footer flex justify-end gap-2">
      <Button
        label={t('buttons.reset')}
        icon="pi pi-undo"
        className="p-button-secondary"
        onClick={handleReset}
        type="button"
      />
      <Button
        label={isEditMode ? t('buttons.save') : t('buttons.saveParty')}
        icon="pi pi-save"
        className="p-button-success"
        loading={loading}
        type="submit"
      />
    </div>
  );

  const formContent = (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Toast ref={toast} />
      <Card className="md:p-4 sm:px-2 sm:py-2 border-1 border-gray-500" footer={footer}>
        {/* Role */}
        <div className="formgroup-inline gap-4 mb-6">
          <h3 className="text-lg font-semibold mb-2">{t('sections.role')}:</h3>
          <div className="flex gap-8">
            <div className="flex items-center">
              <RadioButton
                inputId="customer"
                value="Customer"
                name="role"
                onChange={(e) => setRole(e.value)}
                checked={role === "Customer"}
                disabled={isEditMode}
              />
              <label htmlFor={isEditMode ? '' : "customer"} className="ml-2">{t('roles.customer')}</label>
            </div>
            <div className="flex items-center">
              <RadioButton
                inputId="supplier"
                value="Supplier"
                name="role"
                onChange={(e) => setRole(e.value)}
                checked={role === "Supplier"}
                disabled={isEditMode}
              />
              <label htmlFor={isEditMode ? '' : "supplier"} className="ml-2">{t('roles.supplier')}</label>
            </div>
          </div>
        </div>

        {/* Basic Details */}
        <h3 className="text-lg font-semibold mb-3">{t('sections.basicDetails')}</h3>
        <div className="grid md:grid-cols-4 sm:grid-cols-1 gap-4 mb-4">
          <div className="field pl-2 flex flex-column">
            <label htmlFor="name" className="block font-medium mb-1 ">
              {t('labels.name')}<span className="text-red-500">*</span>
            </label>
            <InputText {...register("name", {
              required: t('validation.nameRequired'), 
              maxLength: {
                value: 100,
                message: t('validation.nameMaxLength'),
              }
            })} className={classNames({ "p-invalid": errors.name })} aria-labelledby="address" />
            {errors.name && <small className="p-error">{errors.name.message}</small>}
          </div>
          <div className="field pl-2 flex flex-column">
            <label htmlFor="email" className="block font-medium mb-1">
              {t('labels.email')}
            </label>
            <InputText {...register("email", {
              validate: (value) => {
                if (!value) return true;
                if (value.length < 6) return t('validation.emailMinLength');
                if (value.length > 100) return t('validation.emailMaxLength');
                const pattern = /^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+$/;
                if (!pattern.test(value)) return t('validation.invalidEmailFormat');
                return true;
              }
            })} />
            {errors.email && <p className="p-error">{errors.email.message}</p>}
          </div>
          <div className="field pl-2 flex flex-column">
            <label htmlFor="contact" className="block font-medium mb-1">
              {t('labels.contact')} <span className="text-red-500">*</span>
            </label>
            <InputText
              {...register("contact", {
                required: t('validation.contactRequired'),
                minLength: { value: 6, message: t('validation.contactMinLength') },
                maxLength: { value: 12, message: t('validation.contactMaxLength') },
                pattern: { value: /^[0-9]+$/, message: t('validation.contactNumbersOnly') }
              })}
              keyfilter="int"
            />
            {errors.contact && <small className="p-error">{errors.contact.message}</small>}
          </div>
        </div>

        <Divider />

        {/* Address */}
        <h3 className="text-lg font-semibold mb-3">{t('sections.address')}</h3>
        <div className="grid md:grid-cols-4 sm:grid-cols-1 gap-4 mb-4">
          <div className="field pl-2">
            <label htmlFor="address" className="block font-medium mb-1">
              {t('labels.address')} <span className="text-red-500">*</span>
            </label>
            <InputText {...register("address", { required: t('validation.addressRequired') })} className={classNames({ "p-invalid": errors.address })} />
            {errors.address && <small className="p-error">{errors.address.message}</small>}
          </div>
              <div className="field md:grid-cols-4 pl-2">
            <label htmlFor="state" className="block font-medium mb-1">
              {t('labels.state')} <span className="text-red-500">*</span>
            </label>
            <Dropdown
              options={states}
              filter
              showClear
              value={watch('state')}
              onChange={(e) => {
                const event = { target: { name: 'state', value: e.value } };
                register('state').onChange(event);
              }}
              className={classNames('w-full', { 'p-invalid': errors.state })}
              placeholder={t('placeholders.selectState')}
              aria-describedby="state-error"
            />
            {errors.state && <small id="state-error" className="p-error">{errors.state.message}</small>}
          </div>
          <div className="field pl-2">
            <label htmlFor="pincode" className="block font-medium mb-1">
              {t('labels.pincode')} <span className="text-red-500">*</span>
            </label>
            <InputText {...register("pincode", { required: t('validation.pincodeRequired') })} keyfilter="int" className={classNames({ "p-invalid": errors.pincode })} />
            {errors.pincode && <small className="p-error">{errors.pincode.message}</small>}
          </div>
        </div>

        <Divider />

        {/* Tax Info */}
        <h3 className="text-lg font-semibold mb-3">{t('sections.taxInfo')}</h3>
        <div className="grid md:grid-cols-4 sm:grid-cols-1 gap-4 mb-4">
          <div className="field pl-2">
            <label htmlFor="pan" className="block font-medium mb-1">{t('labels.pan')}</label>
            <InputText
              {...register("pan", {
                pattern: {
                  value: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
                  message: t('validation.invalidPanFormat'),
                },
              })}
              className={classNames({ "p-invalid": errors.pan })}
            />
            {errors.pan && <small className="p-error">{errors.pan.message}</small>}
          </div>
          <div className="field pl-2">
            <label htmlFor="gstin" className="block font-medium mb-1">{t('labels.gstin')}</label>
            <InputText
              {...register("gstin", {
                pattern: {
                  value: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
                  message: t('validation.invalidGstinFormat'),
                },
              })}
              className={classNames({ "p-invalid": errors.gstin })}
            />
            {errors.gstin && <small className="p-error">{errors.gstin.message}</small>}
          </div>
        </div>

        <Divider />

        {/* Bank Details */}
        <h3 className="text-lg font-semibold mb-3">{t('sections.bankDetails')}</h3>
        <div className="grid md:grid-cols-4 sm:grid-cols-1 gap-4 mb-4 pl-2">
          <div className="field">
            <label htmlFor="accName" className="block font-medium mb-1">{t('labels.accName')}</label>
            <InputText {...register("accName")} />
          </div>
          <div className="field">
            <label htmlFor="accNo" className="block font-medium mb-1 pl-2">{t('labels.accNo')}</label>
            <InputText {...register("accNo")} />
          </div>
          <div className="field">
            <label htmlFor="branch" className="block font-medium mb-1 pl-2">{t('labels.branch')}</label>
            <InputText {...register("branch")} />
          </div>
          <div className="field">
            <label htmlFor="ifsc" className="block font-medium mb-1 pl-2">{t('labels.ifsc')}</label>
            <InputText {...register("ifsc")} />
          </div>
        </div>
      </Card>
    </form>
  )

  if (party) {
    return (
      <Dialog
        visible={visible}
        onHide={onHide}
        header={t('dialog.editParty')}
        className="edit-modal text-primary"
        modal
      >
        {formContent}
      </Dialog>
    );
  } else {
    return (
      <div>
        <Toast ref={toast} className="absolute" />
        {formContent}
      </div>
    )
  }
}