'use client';

import { useOrg } from '../../../contexts/orgContext';

const OrgHeader = () => {
  const { orgname } = useOrg();
  return (
      <div className="md:sticky top-0 z-10 bg-white card px-3 py-1 sm:px-4 sm:py-2 shadow-2 rounded-md mb-2 sm:mb-2 text-base sm:text-lg" style={{ zIndex: 99 }}>
        <h3 className="text-2xl sm:text-3xl text-primary font-bold m-0">
          {orgname || 'Welcome to Invoicing'}
        </h3>
      </div> 
  );
}

export default OrgHeader