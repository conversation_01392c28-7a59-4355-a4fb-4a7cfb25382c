'use client';
import { useRouter } from 'next/navigation';
import { Button } from 'primereact/button';

const BackButton = () => {
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  return <Button
    icon='pi pi-arrow-left'
    className='p-1 h-10 mt-2 text-xl'
    style={{
      background: 'none',
      border: 'none',
      color: '#3B82F6',
      width: '55px',
      fontSize: '89px',
    }}
    onClick={handleBack}
  />
};

export default BackButton;