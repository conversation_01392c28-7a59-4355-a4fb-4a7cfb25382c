'use client';
import React, { useState, useEffect } from 'react';
import { Card } from 'primereact/card';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Message } from 'primereact/message';
import { Fieldset } from 'primereact/fieldset';
import { Panel } from 'primereact/panel';
import { Divider } from 'primereact/divider';
import { AxiosError } from 'axios';
import { getOrgDataField } from '../../../utils/cookies';
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import "../../../styles/olkcss.scss"; 
import { Button } from 'primereact/button';
import { PDFDownloadLink } from '@react-pdf/renderer';
import InvoicePDF from './InvoicePdf';
import apiCall from '../../../utils/apiCallService';

// Define invoice preferences interface
interface InvoicePrefs {
    logo?: string;
    sign?: string;
    autoinvno?: number;
    tandc?: string;
}

export interface orgFields {
    bankdetails: null | string;
    gstin: string;
    orgpan: string;
    orgaddr: string;
    orgpincode: string;
    invoice_preferences?: InvoicePrefs;
}

export interface details {
    bisdetails: orgFields; // Match InvoicePdf.tsx expectation
    olkstatus?: number; // Matches JSON structure
}

export interface InvoiceContent {
    gsflag: number;
    gstflag: number;
    gstrate: number;
    quantity: number;
    gstamount: number;
    productcode: number;
    productname: string;
    freeQuantity: number;
    pricePerUnit: number;
    productAmount: number;
    taxableAmount: number;
    discountAmount: number;
}

export interface InvoiceParty {
    custname: string;
    custaddr: string;
    custphone: string;
    custemail: string | null;
    state: string;
    pincode: string;
}

export interface Consignee {
    name?: string;
    state?: string;
    address?: string;
    pincode?: string;
}

export interface InvoiceRecord {
    invid: number;
    invoiceno: string;
    ewaybillno: string | null;
    invoicedate: string;
    invnarration: string | null;
    taxflag: number;
    contents: InvoiceContent[];
    amountpaid: string;
    invoicetotal: string;
    icflag: number;
    roundoffflag: number;
    lockflag: number;
    discflag: number;
    taxstate: string | null;
    sourcestate: string | null;
    orgstategstin: string | null;
    attachment: string | null;
    attachmentcount: number;
    orgcode: number;
    custid: number;
    consignee: Consignee | null;
    reverserecharge: string | null;
    bankdetails: string | null;
    transportationmode: string | null;
    vehicleno: string | null;
    dateofsupply: string | null;
    paymentmode: number;
    reversecharge: number | null;
    address: string | null;
    pincode: string | null;
    inoutflag: number;
    originaldate: string | null;
    invoicetotalword: string | null;
}

export interface ApiResponse {
    invrecord: InvoiceRecord;
    custdetails?: InvoiceParty; // Made optional to handle potential absence
}

interface CombinedData extends ApiResponse {
    orgDetails: details | null;
}

interface InvoiceViewProps {
    invoiceId: string;
}

const InvoiceView: React.FC<InvoiceViewProps> = ({ invoiceId }) => {
    const { OLK_PATH } = useEnvContext();
    const [invoice, setInvoice] = useState<ApiResponse | null>(null);
    const [combinedData, setCombinedData] = useState<CombinedData | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const orgname = getOrgDataField("orgname");
    const orgCode = Number(getOrgDataField("orgcode"));

    useEffect(() => {
        const fetchInvoice = async () => {
            if (!OLK_PATH) {
                setError("API base URL is not configured");
                setLoading(false);
                return;
            }

            if (!invoiceId) {
                setError("No invoice ID provided");
                setLoading(false);
                return;
            }

            setLoading(true);
            setError(null);
            try {
                const response = await apiCall<ApiResponse>("GET", `${OLK_PATH}/invoice/findinvoice?invid=${invoiceId}`);
                if (!response.data?.invrecord) {
                    throw new Error("No invoice record found");
                }
                setInvoice(response.data);
            } catch (err: unknown) {
                let errorMessage = "Failed to fetch invoice details.";
                if (err instanceof AxiosError) {
                    if (err.response?.status === 404) {
                        errorMessage = `Invoice not found for invid: ${invoiceId}`;
                    } else {
                        errorMessage = `Failed to fetch invoice: ${err.message}`;
                    }
                } else if (err instanceof Error) {
                    errorMessage = `Failed to fetch invoice: ${err.message}`;
                }

                setError(errorMessage);
                console.error("Error details:", err);
            } finally {
                setLoading(false);
            }
        };

        fetchInvoice();
    }, [invoiceId, OLK_PATH]);

    // Function to fetch orgDetails and combine with invoice
    useEffect(() => {
        const fetchOrgDetails = async () => {
            if (!invoice) {
                return;
            }

            if (!OLK_PATH || !orgCode) {
                console.error("Missing OLK_PATH or orgCode");
                return;
            }

            try {
                const orgResponse = await apiCall<details>("GET", `${OLK_PATH}/organisations/bdt?orgcode=${orgCode}`);
                const combined: CombinedData = {
                    ...invoice,
                    orgDetails: orgResponse.data,
                };
                setCombinedData(combined);
            } catch (err: unknown) {
                console.error("Error fetching orgDetails:", err);
                // Set combined data without org details to prevent blocking
                setCombinedData({
                    ...invoice,
                    orgDetails: null,
                });
            }
        };

        // Only fetch org details if we have invoice data
        if (invoice && invoice.invrecord) {
            fetchOrgDetails();
        }
    }, [invoice, OLK_PATH, orgCode]);

    if (loading) {
        return (
            <ProgressSpinner
                style={{ display: "block", margin: "2rem auto" }}
            />
        );
    }

    if (error) {
        return (
            <Message
                severity="error"
                text={error}
                style={{ display: "block", margin: "2rem" }}
            />
        );
    }

    if (!invoice || !invoice.invrecord) {
        return (
            <Message
                severity="warn"
                text="No invoice data available."
                style={{ display: "block", margin: "2rem" }}
            />
        );
    }

    return (
        <div className="invoice-view p-m-4">
            <Card
                title={
                    invoice.invrecord.inoutflag === 15
                        ? "Sales Invoice"
                        : invoice.invrecord.inoutflag === 9
                        ? "Purchase Invoice"
                        : "Unknown Invoice"
                }
            >
                {" "}
                <div className="invoice-header ">
                    <div className="invoice-header">{orgname}</div>
                    <div className="invoice-header">{`Invoice No : ${invoice.invrecord.invoiceno}`}</div>
                    <div className="invoice-header">
                        {`Invoice Date : ${new Date(
                            invoice.invrecord.invoicedate
                        )
                            .toLocaleDateString("en-GB", {
                                day: "2-digit",
                                month: "2-digit",
                                year: "numeric",
                            })
                            .split("/")
                            .join("-")}`}
                    </div>
                </div>
                <Divider />
                {/* Customer and Consignee Details */}
                <div className="p-grid invoice-section">
                    <div className="p-col-6 p-md-6">
                        <Fieldset
                            legend={
                                invoice.invrecord.inoutflag === 15
                                    ? "Customer Details"
                                    : "Supplier details"
                            }
                            className="p-shadow-2"
                        >
                            <div className="p-field">
                                <label>
                                    <strong>Name : </strong>
                                </label>
                                <span className="ml-2">
                                    {invoice.custdetails?.custname || "N/A"}
                                </span>
                            </div>

                            <div className="p-grid p-formgrid">
                                <div className="p-col-3 p-field">
                                    <label>
                                        <strong>Phone : </strong>
                                    </label>
                                    <span className="ml-2">
                                        {invoice.custdetails?.custphone ||
                                            "N/A"}
                                    </span>
                                </div>
                                <div className="p-col-3 p-field">
                                    <label>
                                        <strong>Email : </strong>
                                    </label>
                                    <span className="ml-2">
                                        {invoice.custdetails?.custemail ||
                                            "N/A"}
                                    </span>
                                </div>
                            </div>

                            <div className="p-field">
                                <label>
                                    <strong>Address : </strong>
                                </label>
                                <span className="ml-2">
                                    {invoice.custdetails?.custaddr || "N/A"}
                                </span>
                            </div>

                            <div className="p-grid p-formgrid">
                                <div className="p-col-3 p-field">
                                    <label>
                                        <strong>State : </strong>
                                    </label>
                                    <span className="ml-2">
                                        {invoice.custdetails?.state || "N/A"}
                                    </span>
                                </div>
                                <div className="p-col-3 p-field">
                                    <label>
                                        <strong>Pincode : </strong>
                                    </label>
                                    <span className="ml-2">
                                        {invoice.custdetails?.pincode || "N/A"}
                                    </span>
                                </div>
                            </div>
                        </Fieldset>
                    </div>
                    <Divider />
                    {invoice.invrecord.consignee && (
                        <div className="p-col-12 p-md-6">
                            <Fieldset
                                legend="Consignee Details"
                                className="p-shadow-2"
                            >
                                <div className="p-field">
                                    <label className="p-d-block">
                                        <strong>Consignee Name : </strong>
                                    </label>
                                    <span>
                                        {invoice.invrecord.consignee?.name ||
                                            "N/A"}
                                    </span>
                                </div>
                                <div className="p-field">
                                    <label className="p-d-block">
                                        <strong>Consignee Address : </strong>
                                    </label>
                                    <span>
                                        {invoice.invrecord.consignee?.address ||
                                            "N/A"}
                                    </span>
                                </div>
                                <div className="p-field">
                                    <label className="p-d-block">
                                        <strong>Consignee Address : </strong>
                                    </label>
                                    <span>
                                        {invoice.invrecord.consignee?.state ||
                                            "N/A"}
                                    </span>
                                </div>
                                <div className="p-field">
                                    <label className="p-d-block">
                                        <strong>Consignee Address : </strong>
                                    </label>
                                    <span>
                                        {invoice.invrecord.consignee?.pincode ||
                                            "N/A"}
                                    </span>
                                </div>
                            </Fieldset>
                        </div>
                    )}
                </div>
                <Divider />
                {/* Product Table */}
                <Panel header="Products" className="p-mb-2 p-shadow-2">
                    <DataTable
                        value={invoice.invrecord.contents}
                        responsiveLayout="stack"
                        className="p-datatable-sm invoice-products-table"
                    >
                        <Column
                            field="productname"
                            header="Product Name"
                            className="p-text-bold"
                        />
                        <Column
                            field="quantity"
                            header="Quantity"
                            align="right"
                        />
                        <Column
                            field="freeQuantity"
                            header="Free Qty"
                            align="right"
                        />
                        <Column
                            field="pricePerUnit"
                            header="Price/Unit"
                            align="right"
                            body={(rowData) =>
                                `₹${rowData.pricePerUnit.toFixed(2)}`
                            }
                        />
                        <Column
                            field="discountAmount"
                            header="Discount"
                            align="right"
                            body={(rowData) =>
                                invoice.invrecord.discflag === 1
                                    ? `₹${(rowData.discountAmount || 0).toFixed(
                                          2
                                      )}`
                                    : `${rowData.discountPercent || 0}%`
                            }
                        />
                        <Column
                            field="taxableAmount"
                            header="Taxable Amt"
                            align="right"
                            body={(rowData) =>
                                `₹${rowData.taxableAmount.toFixed(2)}`
                            }
                        />

                        <Column
                            field="gstrate"
                            header="GST Rate"
                            align="right"
                            body={(rowData) => `${rowData.gstrate}%`}
                        />
                        <Column
                            field="gstamount"
                            header="GST Amount"
                            align="right"
                            body={(rowData) =>
                                `₹${rowData.gstamount.toFixed(2)}`
                            }
                        />

                        <Column
                            field="productAmount"
                            header="Total Amount"
                            align="right"
                            body={(rowData) =>
                                `₹${rowData.productAmount.toFixed(2)}`
                            }
                        />
                    </DataTable>
                </Panel>
                <Divider />
                {/* Payment Details and Invoice Summary Side by Side */}
                <div className="p-grid">
                    {/* Payment Details */}
                    <div className="p-col-12 p-md-6">
                        <Panel header="Payment Details" className="p-shadow-2">
                            <div className="p-grid">
                                <div className="p-col-12 p-md-6">
                                    <div className="p-field">
                                        <label>
                                            <strong>Payment Mode:</strong>
                                        </label>
                                        <span className="ml-2">
                                            {invoice.invrecord.paymentmode ===
                                            15
                                                ? "On Credit"
                                                : invoice.invrecord
                                                      .paymentmode === 3
                                                ? "Cash Received"
                                                : invoice.invrecord
                                                      .paymentmode === 2
                                                ? "Bank Transfer"
                                                : "Unknown"}
                                        </span>
                                    </div>
                                </div>
                                <div className="p-col-12 p-md-6">
                                    <div className="p-field">
                                        <label>
                                            <strong>Bank Details:</strong>
                                        </label>
                                        <span className="ml-2">
                                            {invoice.invrecord.bankdetails ||
                                                "N/A"}
                                        </span>
                                    </div>
                                </div>
                                <div className="p-col-12 p-md-6">
                                    <div className="p-field">
                                        <label>
                                            <strong>Reverse Charge:</strong>
                                        </label>
                                        <span className="ml-2">
                                            {invoice.invrecord.reversecharge ===
                                                0 ||
                                            invoice.invrecord.reversecharge ===
                                                null
                                                ? "No"
                                                : "Yes"}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </Panel>
                    </div>

                    {/* Invoice Summary */}
                    <div className="p-col-12 p-md-6">
                        <Panel header="Invoice Summary" className="p-shadow-2">
                            <div className="p-field">
                                <label>
                                    <strong>Total Taxable Amount:</strong>
                                </label>
                                <span className="ml-2">
                                    ₹
                                    {invoice.invrecord.contents
                                        .reduce(
                                            (sum, item) =>
                                                sum + item.taxableAmount,
                                            0
                                        )
                                        .toFixed(2)}
                                </span>
                            </div>
                            <div className="p-field">
                                <label>
                                    <strong>Total Discount:</strong>
                                </label>
                                <span className="ml-2">
                                    ₹
                                    {invoice.invrecord.contents
                                        .reduce(
                                            (sum, item) =>
                                                sum +
                                                (item.discountAmount || 0),
                                            0
                                        )
                                        .toFixed(2)}
                                </span>
                            </div>

                            {invoice.invrecord.taxstate ===
                            invoice.invrecord.sourcestate ? (
                                <>
                                    <div className="p-field">
                                        <label>
                                            <strong>Total CGST:</strong>
                                        </label>
                                        <span className="ml-2">
                                            ₹
                                            {(
                                                invoice.invrecord.contents.reduce(
                                                    (sum, item) =>
                                                        sum + item.gstamount,
                                                    0
                                                ) / 2
                                            ).toFixed(2)}
                                        </span>
                                    </div>
                                    <div className="p-field">
                                        <label>
                                            <strong>Total SGST:</strong>
                                        </label>
                                        <span className="ml-2">
                                            ₹
                                            {(
                                                invoice.invrecord.contents.reduce(
                                                    (sum, item) =>
                                                        sum + item.gstamount,
                                                    0
                                                ) / 2
                                            ).toFixed(2)}
                                        </span>
                                    </div>
                                </>
                            ) : (
                                <div className="p-field">
                                    <label>
                                        <strong>Total IGST:</strong>
                                    </label>
                                    <span className="ml-2">
                                        ₹
                                        {invoice.invrecord.contents
                                            .reduce(
                                                (sum, item) =>
                                                    sum + item.gstamount,
                                                0
                                            )
                                            .toFixed(2)}
                                    </span>
                                </div>
                            )}

                            <div className="p-field">
                                <label>
                                    <strong>Total Invoice Amount:</strong>
                                </label>
                                <span className="ml-2">
                                    ₹{invoice.invrecord.invoicetotal}
                                </span>
                            </div>
                            <div className="p-field">
                                <label>
                                    <strong>Total Paid Amount:</strong>
                                </label>
                                <span className="ml-2">
                                    ₹{invoice.invrecord.amountpaid}
                                </span>
                            </div>
                        </Panel>
                    </div>
                </div>
                <Divider />
                {/* Mode of Transportation */}
                {(invoice.invrecord.transportationmode ||
                    invoice.invrecord.transportationmode == "None" ||
                    invoice.invrecord.vehicleno ||
                    invoice.invrecord.dateofsupply) && (
                    <Fieldset
                        legend="Mode of Transportation"
                        className="p-mb-4 p-shadow-2"
                    >
                        <div className="p-grid">
                            <div className="p-col-12 p-md-6">
                                {invoice.invrecord.transportationmode && (
                                    <div className="p-field">
                                        <label className="p-d-block">
                                            <strong>
                                                Transportation Mode :{" "}
                                            </strong>
                                        </label>
                                        <span>
                                            {invoice.invrecord
                                                .transportationmode || "N/A"}
                                        </span>
                                    </div>
                                )}
                                {invoice.invrecord.vehicleno && (
                                    <div className="p-field">
                                        <label className="p-d-block">
                                            <strong>Vehicle No : </strong>
                                        </label>
                                        <span>
                                            {invoice.invrecord.vehicleno ||
                                                "N/A"}
                                        </span>
                                    </div>
                                )}
                            </div>
                            <div className="p-col-12 p-md-6">
                                {invoice.invrecord.dateofsupply && (
                                    <div className="p-field">
                                        <label className="p-d-block">
                                            <strong>Date of Supply : </strong>
                                        </label>
                                        <span>
                                            {invoice.invrecord.dateofsupply
                                                ? new Date(
                                                      invoice.invrecord.dateofsupply
                                                  )
                                                      .toLocaleDateString(
                                                          "en-GB",
                                                          {
                                                              day: "2-digit",
                                                              month: "2-digit",
                                                              year: "numeric",
                                                          }
                                                      )
                                                      .split("/")
                                                      .join(" /")
                                                : "N/A"}
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                    </Fieldset>
                )}
                <Divider />
                {/* Invoice Summary */}
                <div className="p-field">
                    <label className="p-d-block">
                        <strong>Total in Words : </strong>
                    </label>
                    <span>{invoice.invrecord.invoicetotalword || "N/A"}</span>
                </div>
                <div className="p-field">
                    <label className="p-d-block">
                        <strong>Narration : </strong>
                    </label>
                    <span>{invoice.invrecord.invnarration || ""}</span>
                </div>
            </Card>

            {invoice && combinedData && (
                <div className="flex">
                    {/* PDF download button - only show when we have combined data */}
                    <PDFDownloadLink
                        document={<InvoicePDF data={combinedData} />}
                        fileName={`invoice_${invoice.invrecord.invoiceno}.pdf`}
                    >
                        {({ loading }) => (
                            <Button
                                text
                                raised
                                label={loading ? "Generating PDF..." : "Print"}
                                icon={
                                    loading
                                        ? "pi pi-spinner pi-spin"
                                        : "pi pi-print"
                                }
                                className="w-full sm:w-auto bg-blue-600 text-lg md:text-2xl px-5 py-3 olk-button mt-3"
                                disabled={loading}
                            />
                        )}
                    </PDFDownloadLink>
                </div>
            )}

            {invoice && !combinedData && (
                <div className="flex">
                    <Button
                        text
                        raised
                        label="Loading PDF data..."
                        icon="pi pi-spinner pi-spin"
                        className="w-full sm:w-auto bg-gray-400 text-lg md:text-2xl px-5 py-3 olk-button mt-3"
                        disabled={true}
                    />
                </div>
            )}
        </div>
    );
};

export default InvoiceView;
