// import React, { useEffect, useState, useRef } from 'react';
// import { DataTable } from 'primereact/datatable';
// import { Column } from 'primereact/column';
// import { Toast } from 'primereact/toast';
// import { Dropdown } from 'primereact/dropdown';
// import { InputNumber } from 'primereact/inputnumber';
// import { InputText } from 'primereact/inputtext';
// import { Button } from 'primereact/button';
// import { ProgressSpinner } from 'primereact/progressspinner';
// import { Card } from 'primereact/card';
// import Cookies from 'js-cookie';
// import axiosInstance from '@/utils/axios';
// import { useEnvContext } from '@/providers/EnvContextProvider';
// import { useTranslations } from 'next-intl';

// interface Product {
//   productcode: number;
//   productdesc: string;
//   gscode: string;
//   gsflag: number;
//   prodsp: string;
//   prodmrp: string;
//   uom_name: string;
//   tax: {
//     IGST: number;
//   };
//   deletable: boolean;
// }

// interface InvoiceItem {
//   id: number;
//   product: Product | null;
//   hsn: string;
//   qty: number;
//   freeQty: number;
//   amount: number;
//   discount: number;
//   discountType: 'amount' | 'percentage';
//   taxableValue: number;
//   gstRate: number;
//   gstAmount: number;
//   total: number;
// }

// const ProductTable: React.FC = () => {
//   const [products, setProducts] = useState<Product[]>([]);
//   const [invoiceItems, setInvoiceItems] = useState<InvoiceItem[]>([
//     {
//       id: Date.now(),
//       product: null,
//       hsn: '',
//       qty: 0.00,
//       freeQty: 0.00,
//       amount: 0.00,
//       discount: 0.00,
//       discountType: 'amount',
//       taxableValue: 0.00,
//       gstRate: 0.00,
//       gstAmount: 0.00,
//       total: 0.00,
//     },
//   ]);
//   const [loading, setLoading] = useState(false);
//   const [searchTerm, setSearchTerm] = useState('');
//   const toast = useRef<Toast>(null);
//   const orgcode = Number(Cookies.get('orgcode'));
//   const { OLK_PATH } = useEnvContext();
//   const t = useTranslations('ProductTable');

//   useEffect(() => {
//     if (!orgcode) return;

//     const fetchProducts = async () => {
//       setLoading(true);
//       try {
//         const response = await axiosInstance.get(`${OLK_PATH}/products?orgcode=${orgcode}`);
//         if (response.data.olkstatus === 1) {
//           toast.current?.show({ 
//             severity: 'error', 
//             summary: t('errors.error'), 
//             detail: t('errors.somethingWentWrong'), 
//             life: 3000 
//           });
//         } else if (response.data.olkstatus === 0 && response.data.olkresult.length === 0) {
//           toast.current?.show({ 
//             severity: 'info', 
//             summary: t('messages.noData'), 
//             detail: t('messages.noProductsFound'), 
//             life: 3000 
//           });
//         } else {
//           setProducts(response.data.olkresult);
//         }
//       } catch (error) {
//         toast.current?.show({ 
//           severity: 'error', 
//           summary: t('errors.apiError'), 
//           detail: t('errors.failedToFetchProducts'), 
//           life: 3000 
//         });
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchProducts();
//   }, [orgcode, OLK_PATH, t]);

//   const addNewRow = () => {
//     const newItem: InvoiceItem = {
//       id: Date.now(),
//       product: null,
//       hsn: '',
//       qty: 0.00,
//       freeQty: 0.00,
//       amount: 0.00,
//       discount: 0.00,
//       discountType: 'amount',
//       taxableValue: 0.00,
//       gstRate: 0.00,
//       gstAmount: 0.00,
//       total: 0.00,
//     };
//     setInvoiceItems([...invoiceItems, newItem]);
//   };

//   const deleteRow = (id: number) => {
//     const updatedItems = invoiceItems.filter((item) => item.id !== id);
//     if (updatedItems.length === 0) {
//       updatedItems.push({
//         id: Date.now(),
//         product: null,
//         hsn: '',
//         qty: 0.00,
//         freeQty: 0.00,
//         amount: 0.00,
//         discount: 0.00,
//         discountType: 'amount',
//         taxableValue: 0.00,
//         gstRate: 0.00,
//         gstAmount: 0.00,
//         total: 0.00,
//       });
//     }
//     setInvoiceItems(updatedItems);
//   };

//   const updateInvoiceItem = (id: number, field: keyof InvoiceItem, value: any) => {
//     setInvoiceItems((prevItems) =>
//       prevItems.map((item) => {
//         if (item.id !== id) return item;

//         const updatedItem = { ...item, [field]: value };

//         if (field === 'product' && value) {
//           updatedItem.hsn = value.gscode || '';
//           updatedItem.gstRate = value.tax?.IGST || 0.00;
//           updatedItem.amount = parseFloat(value.prodsp) || 0.00;
//         }

//         // Calculate taxable value, GST amount, and total
//         const qty = updatedItem.qty || 0;
//         const amount = updatedItem.amount || 0.00;
//         let discount = updatedItem.discount || 0.00;
//         if (updatedItem.discountType === 'percentage') {
//           discount = (amount * qty * discount) / 100;
//         }
//         const taxableValue = amount * qty - discount;
//         const gstAmount = (taxableValue * updatedItem.gstRate) / 100;
//         const total = taxableValue + gstAmount;

//         return {
//           ...updatedItem,
//           taxableValue: parseFloat(taxableValue.toFixed(2)),
//           gstAmount: parseFloat(gstAmount.toFixed(2)),
//           total: parseFloat(total.toFixed(2)),
//         };
//       })
//     );
//   };

//   const calculateTotals = () => {
//     const totals = invoiceItems.reduce(
//       (acc, item) => ({
//         taxableValue: acc.taxableValue + item.taxableValue,
//         gstAmount: acc.gstAmount + item.gstAmount,
//         total: acc.total + item.total,
//       }),
//       { taxableValue: 0.00, gstAmount: 0.00, total: 0.00 }
//     );
//     return {
//       taxableValue: parseFloat(totals.taxableValue.toFixed(2)),
//       gstAmount: parseFloat(totals.gstAmount.toFixed(2)),
//       total: parseFloat(totals.total.toFixed(2)),
//     };
//   };

//   const isInvoiceValid = () => {
//     return invoiceItems.every((item) => item.product !== null);
//   };

//   const totals = calculateTotals();

//   const getAvailableProducts = (currentItemId: number) => {
//     const selectedProductCodes = invoiceItems
//       .filter((item) => item.id !== currentItemId && item.product !== null)
//       .map((item) => item.product!.productcode);
//     return products
//       .filter((product) => !selectedProductCodes.includes(product.productcode))
//       .filter((product) => product.productdesc.toLowerCase().includes(searchTerm.toLowerCase()));
//   };

//   const productBodyTemplate = (rowData: InvoiceItem) => (
//     <Dropdown
//       value={rowData.product}
//       options={getAvailableProducts(rowData.id)}
//       optionLabel="productdesc"
//       placeholder={t('placeholders.selectProduct')}
//       filter
//       onChange={(e) => updateInvoiceItem(rowData.id, 'product', e.value)}
//       onFilter={(e) => setSearchTerm(e.filter)}
//       className="w-full"
//     />
//   );

//   const hsnBodyTemplate = (rowData: InvoiceItem) => (
//     <InputText value={rowData.hsn} disabled className="w-full" />
//   );

//   const qtyBodyTemplate = (rowData: InvoiceItem) => (
//     <InputNumber
//       value={rowData.qty}
//       onValueChange={(e) => updateInvoiceItem(rowData.id, 'qty', e.value || 0)}
//       placeholder={t('placeholders.quantity')}
//       className="w-full"
//       inputStyle={{ textAlign: 'right' }}
//     />
//   );

//   const freeQtyBodyTemplate = (rowData: InvoiceItem) => (
//     <InputNumber
//       value={rowData.freeQty}
//       onValueChange={(e) => updateInvoiceItem(rowData.id, 'freeQty', e.value || 0.00)}
//       placeholder={t('placeholders.freeQuantity')}
//       className="w-full"
//       inputStyle={{ textAlign: 'right' }}
//     />
//   );

//   const amountBodyTemplate = (rowData: InvoiceItem) => (
//     <InputNumber
//       value={rowData.amount}
//       onValueChange={(e) => updateInvoiceItem(rowData.id, 'amount', e.value || 0.00)}
//       placeholder={t('placeholders.amount')}
//       className="w-full"
//       inputStyle={{ textAlign: 'right' }}
//     />
//   );

//   const discountBodyTemplate = (rowData: InvoiceItem) => (
//     <div className="flex gap-2">
//       <InputNumber
//         value={rowData.discount}
//         onValueChange={(e) => updateInvoiceItem(rowData.id, 'discount', e.value || 0.00)}
//         placeholder={t('placeholders.discount')}
//         className="w-1/2"
//         inputStyle={{ textAlign: 'right' }}
//       />
//       <Dropdown
//         value={rowData.discountType}
//         options={[
//           { label: t('discountTypes.amount'), value: 'amount' },
//           { label: t('discountTypes.percentage'), value: 'percentage' },
//         ]}
//         onChange={(e) => updateInvoiceItem(rowData.id, 'discountType', e.value)}
//         className="w-1/2"
//       />
//     </div>
//   );

//   const taxableValueBodyTemplate = (rowData: InvoiceItem) => (
//     <InputText value={rowData.taxableValue.toFixed(2)} disabled className="w-full" style={{ textAlign: 'right' }} />
//   );

//   const gstRateBodyTemplate = (rowData: InvoiceItem) => (
//     <InputText value={`${rowData.gstRate}%`} disabled className="w-full" style={{ textAlign: 'right' }} />
//   );

//   const gstAmountBodyTemplate = (rowData: InvoiceItem) => (
//     <InputText value={rowData.gstAmount.toFixed(2)} disabled className="w-full" style={{ textAlign: 'right' }} />
//   );

//   const totalBodyTemplate = (rowData: InvoiceItem) => (
//     <InputText value={rowData.total.toFixed(2)} disabled className="w-full" style={{ textAlign: 'right' }} />
//   );

//   const actionBodyTemplate = (rowData: InvoiceItem, { rowIndex }: { rowIndex: number }) => {
//     if (rowIndex === 0) return null;
//     return (
//       <Button
//         icon="pi pi-trash"
//         className="p-button-danger p-button-sm"
//         onClick={() => deleteRow(rowData.id)}
//       />
//     );
//   };

//   const renderCardView = (item: InvoiceItem, index: number) => (
//     <Card key={item.id} className="mb-4">
//       <div className="grid grid-cols-1 gap-4">
//         <div>
//           <label className="block text-sm font-medium mb-1">{t('labels.product')}</label>
//           {productBodyTemplate(item)}
//         </div>
//         <div>
//           <label className="block text-sm font-medium mb-1">{t('labels.hsn')}</label>
//           {hsnBodyTemplate(item)}
//         </div>
//         <div>
//           <label className="block text-sm font-medium mb-1">{t('labels.quantity')}</label>
//           {qtyBodyTemplate(item)}
//         </div>
//         <div>
//           <label className="block text-sm font-medium mb-1">{t('labels.freeQuantity')}</label>
//           {freeQtyBodyTemplate(item)}
//         </div>
//         <div>
//           <label className="block text-sm font-medium mb-1">{t('labels.amount')}</label>
//           {amountBodyTemplate(item)}
//         </div>
//         <div>
//           <label className="block text-sm font-medium mb-1">{t('labels.discount')}</label>
//           {discountBodyTemplate(item)}
//         </div>
//         <div>
//           <label className="block text-sm font-medium mb-1">{t('labels.taxableValue')}</label>
//           {taxableValueBodyTemplate(item)}
//         </div>
//         <div>
//           <label className="block text-sm font-medium mb-1">{t('labels.gstRate')}</label>
//           {gstRateBodyTemplate(item)}
//         </div>
//         <div>
//           <label className="block text-sm font-medium mb-1">{t('labels.gstAmount')}</label>
//           {gstAmountBodyTemplate(item)}
//         </div>
//         <div>
//           <label className="block text-sm font-medium mb-1">{t('labels.total')}</label>
//           {totalBodyTemplate(item)}
//         </div>
//         <div className="flex justify-end">
//           {index !== 0 && actionBodyTemplate(item, { rowIndex: index })}
//         </div>
//       </div>
//     </Card>
//   );

//   return (
//     <div className="">
//       <Toast ref={toast} />
//       {loading ? (
//         <div className="flex justify-center items-center h-48">
//           <ProgressSpinner />
//         </div>
//       ) : (
//         <div className="flex flex-col gap-4">
//           {/* Card view for mobile (hidden on md and above) */}
//           <div className="block md:hidden">
//             {invoiceItems.map((item, index) => renderCardView(item, index))}
//             <div className="flex flex-col gap-4 mt-4">
//               <div className="flex justify-end gap-2">
//                 <Button
//                   label={t('buttons.addProduct')}
//                   icon="pi pi-plus"
//                   className="p-button-sm w-auto"
//                   onClick={addNewRow}
//                 />
//               </div>
//             </div>
//           </div>
//           {/* Table view for desktop (hidden on mobile) */}
//           <div className="hidden md:block">
//             <DataTable
//               value={invoiceItems}
//               className="w-full"
//               footer={
//                 <div className="flex justify-between items-center gap-4 text-right font-bold">
//                   <div className="flex gap-2">
//                     <Button
//                       label={t('buttons.addProduct')}
//                       icon="pi pi-plus"
//                       className="p-button-sm w-auto"
//                       onClick={addNewRow}
//                     />
//                   </div>
//                   <div className="flex justify-end flex-wrap gap-4">
//                     <div>{t('labels.taxableValue')}: {t('currencySymbol')}{totals.taxableValue}</div>
//                     <div>{t('labels.gstAmount')}: {t('currencySymbol')}{totals.gstAmount}</div>
//                     <div>{t('labels.total')}: {t('currencySymbol')}{totals.total}</div>
//                   </div>
//                 </div>
//               }
//             >
//               <Column header={t('headers.product')} body={productBodyTemplate} style={{ minWidth: '200px' }} />
//               <Column header={t('headers.hsn')} body={hsnBodyTemplate} style={{ minWidth: '100px' }} />
//               <Column header={t('headers.quantity')} body={qtyBodyTemplate} style={{ minWidth: '100px' }} />
//               <Column header={t('headers.freeQuantity')} body={freeQtyBodyTemplate} style={{ minWidth: '100px' }} />
//               <Column header={t('headers.amount')} body={amountBodyTemplate} style={{ minWidth: '120px' }} />
//               <Column header={t('headers.discount')} body={discountBodyTemplate} style={{ minWidth: '150px' }} />
//               <Column header={t('headers.taxableValue')} body={taxableValueBodyTemplate} style={{ minWidth: '120px' }} />
//               <Column header={t('headers.gst')} body={gstRateBodyTemplate} style={{ minWidth: '100px' }} />
//               <Column header={t('headers.gstAmount')} body={gstAmountBodyTemplate} style={{ minWidth: '120px' }} />
//               <Column header={t('headers.total')} body={totalBodyTemplate} style={{ minWidth: '120px' }} />
//               <Column header={t('headers.action')} body={actionBodyTemplate} style={{ minWidth: '80px' }} />
//             </DataTable>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };

// export default ProductTable;