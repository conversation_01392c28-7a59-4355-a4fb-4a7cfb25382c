"use client";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { InputText } from "primereact/inputtext";
import { Button } from "primereact/button";
import { Message } from "primereact/message";

interface PaymentReceiptFormProps {
    invoiceId?: string;
    invoiceType?: "sales" | "purchase";
}

interface InvoiceDetails {
    invoiceNo: string;
    invoiceDate: string;
    partyName: string;
    totalAmount: string;
    amountPaid: string;
}

//  PaymentReceiptForm handles both payment (purchase) and receipt (sales) entries for invoices.
// It pre-fills invoice details, shows pending amount, and allows entering payment/receipt amount.

const PaymentReceiptForm: React.FC<PaymentReceiptFormProps> = ({
    invoiceId,
    invoiceType,
}) => {
    const router = useRouter();
    const searchParams = useSearchParams();
    // Prefer props, fallback to query params
    const id = invoiceId || searchParams.get("invoiceId") || "";
    const type =
        invoiceType ||
        (searchParams.get("invoiceType") as "sales" | "purchase") ||
        "sales";

    // Simulated invoice details (replace with API call)
    const [invoice, setInvoice] = useState<InvoiceDetails | null>(null);
    const [pendingAmount, setPendingAmount] = useState<string>("0.00");
    const [amountPaid, setAmountPaid] = useState<string>("0.00");
    const [error, setError] = useState<string | null>(null);
    const [paymentDate, setPaymentDate] = useState<string>("");

    // Simulate fetching invoice details
    useEffect(() => {
        // TODO: Replace with real API call
        setTimeout(() => {
            const mock: InvoiceDetails = {
                invoiceNo: "INV-00123",
                invoiceDate: "12/06/2024",
                partyName:
                    type === "sales"
                        ? "Acme Customer Pvt Ltd"
                        : "Best Supplier LLP",
                totalAmount: "10000.00",
                amountPaid: "7000.00",
            };
            setInvoice(mock);
            const pending = (
                parseFloat(mock.totalAmount) - parseFloat(mock.amountPaid)
            ).toFixed(2);
            setPendingAmount(pending);
            setAmountPaid(pending); // Default to pending
        }, 300);
    }, [id, type]);

    // Handle amount paid change
    const handleAmountPaidChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAmountPaid(e.target.value);
    };

    // Handle Save (stub)
    const handleSave = () => {
        if (
            !amountPaid ||
            isNaN(Number(amountPaid)) ||
            Number(amountPaid) <= 0
        ) {
            setError("Please enter a valid amount.");
            return;
        }
        if (!paymentDate) {
            setError("Please select a date for the payment/receipt.");
            return;
        }
        setError(null);
        // Simulate success and redirect
        setTimeout(() => {
            router.push("/dashboard/invoice/sales-and-purchase/list");
        }, 500);
    };

    // Handle Cancel
    const handleCancel = () => {
        router.back();
    };

    if (!invoice) {
        return <div className="p-4 text-center text-gray-500">Loading...</div>;
    }

    return (
        <div className="flex justify-center items-start pt-2 px-4">
            <div className="max-w-3xl bg-white rounded-lg shadow-md p-4 m-auto">
                <h2 className="text-xl font-semibold mb-4 text-center">
                    {type === "sales" ? "Receipt" : "Payment"} Entry
                </h2>
                {error && (
                    <Message severity="error" text={error} className="mb-3" />
                )}
                <div className="flex gap-3 mb-3">
                    <div className="flex-1">
                        <label className="block font-medium mb-1">
                            Invoice No
                        </label>
                        <div className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200 text-gray-700 text-xl">
                            {invoice.invoiceNo}
                        </div>
                    </div>
                    <div className="flex-1">
                        <label className="block font-medium mb-1">Date</label>
                        <div className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200 text-gray-700 text-xl">
                            {invoice.invoiceDate}
                        </div>
                    </div>
                </div>
                <div className="flex gap-3 mb-3">
                    <div className="flex-1">
                        <label className="block font-medium mb-1">
                            {type === "sales" ? "Customer" : "Supplier"} Details
                        </label>
                        <div className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200 text-gray-700 text-xl">
                            {invoice.partyName}
                        </div>
                    </div>
                    <div className="flex-1">
                        <label className="block font-medium mb-1">
                            Total Invoice Amount
                        </label>
                        <div className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200 text-gray-700 text-xl">
                            {invoice.totalAmount}
                        </div>
                    </div>
                </div>
                <div className="flex gap-3 mb-3">
                    <div className="flex-1">
                        <label className="block font-medium mb-1">
                            Pending Amount
                        </label>
                        <div className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200 text-red-600 font-semibold text-xl">
                            {pendingAmount}
                        </div>
                    </div>
                    <div className="flex-1">
                        <label className="block font-medium mb-1">
                            {type === "sales" ? "Receipt" : "Payment"} Date{" "}
                            <span className="text-red-500">*</span>
                        </label>
                        <InputText
                            type="date"
                            value={paymentDate}
                            onChange={(e) => setPaymentDate(e.target.value)}
                            className="w-full"
                            required
                        />
                    </div>
                </div>
                <div className="mb-3">
                    <label className="block font-medium mb-1">
                        Amount Paid
                    </label>
                    <InputText
                        value={amountPaid}
                        onChange={handleAmountPaidChange}
                        className="w-full text-xl"
                        type="number"
                        min="0"
                        max={pendingAmount}
                    />
                </div>
                <div className="flex gap-3 mt-4 justify-end">
                    <Button
                        label="Save"
                        icon="pi pi-check"
                        onClick={handleSave}
                        className="p-button-success"
                    />
                    <Button
                        label="Cancel"
                        icon="pi pi-times"
                        onClick={handleCancel}
                        className="p-button-secondary"
                    />
                </div>
            </div>
        </div>
    );
};

export default PaymentReceiptForm;
