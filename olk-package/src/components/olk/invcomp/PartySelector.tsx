// 'use client';

// import React, { useEffect, useState, useRef } from 'react';
// import { Dropdown } from 'primereact/dropdown';
// import { Divider } from 'primereact/divider';
// import { Checkbox } from 'primereact/checkbox';
// import { InputText } from 'primereact/inputtext';
// import axiosInstance from '@/utils/axios';
// import { getOrgDataField } from '@/utils/cookies';
// import { ProgressSpinner } from 'primereact/progressspinner';
// import { Toast } from 'primereact/toast';
// import { useEnvContext } from "@/providers/EnvContextProvider";
// import { useTranslations } from 'next-intl';

// interface Party {
//   custid: number;
//   custname: string;
//   gstin: string;
//   custaddr: string;
//   state: string;
//   pincode: string;
// }

// export default function ReceiverDetails() {
//   const t = useTranslations('ReceiverDetails');  // <-- Using namespace 'ReceiverDetails'
//   const { OLK_PATH } = useEnvContext();

//   const [parties, setParties] = useState<Party[]>([]);
//   const [selectedParty, setSelectedParty] = useState<Party | null>(null);
//   const [loading, setLoading] = useState(false);
//   const [sameAsBilling, setSameAsBilling] = useState(true);
//   const [consignee, setConsignee] = useState({
//     name: '',
//     address: '',
//     state: '',
//     pincode: '',
//   });

//   const states = [
//     { label: t('states.Jammu and Kashmir'), value: 'Jammu and Kashmir' },
//     { label: t('states.Himachal Pradesh'), value: 'Himachal Pradesh' },
//     { label: t('states.Punjab'), value: 'Punjab' },
//     { label: t('states.Chandigarh'), value: 'Chandigarh' },
//     { label: t('states.Uttarakhand'), value: 'Uttarakhand' },
//     { label: t('states.Haryana'), value: 'Haryana' },
//     { label: t('states.Delhi'), value: 'Delhi' },
//     { label: t('states.Rajasthan'), value: 'Rajasthan' },
//     { label: t('states.Uttar Pradesh'), value: 'Uttar Pradesh' },
//     { label: t('states.Bihar'), value: 'Bihar' },
//     { label: t('states.Sikkim'), value: 'Sikkim' },
//     { label: t('states.Arunachal Pradesh'), value: 'Arunachal Pradesh' },
//     { label: t('states.Nagaland'), value: 'Nagaland' },
//     { label: t('states.Manipur'), value: 'Manipur' },
//     { label: t('states.Mizoram'), value: 'Mizoram' },
//     { label: t('states.Tripura'), value: 'Tripura' },
//     { label: t('states.Meghalaya'), value: 'Meghalaya' },
//     { label: t('states.Assam'), value: 'Assam' },
//     { label: t('states.West Bengal'), value: 'West Bengal' },
//     { label: t('states.Jharkhand'), value: 'Jharkhand' },
//     { label: t('states.Odisha'), value: 'Odisha' },
//     { label: t('states.Chhattisgarh'), value: 'Chhattisgarh' },
//     { label: t('states.Madhya Pradesh'), value: 'Madhya Pradesh' },
//     { label: t('states.Gujarat'), value: 'Gujarat' },
//     { label: t('states.Daman and Diu'), value: 'Daman and Diu' },
//     { label: t('states.Dadra and Nagar Haveli'), value: 'Dadra and Nagar Haveli' },
//     { label: t('states.Maharashtra'), value: 'Maharashtra' },
//     { label: t('states.Andhra Pradesh'), value: 'Andhra Pradesh' },
//     { label: t('states.Karnataka'), value: 'Karnataka' },
//     { label: t('states.Goa'), value: 'Goa' },
//     { label: t('states.Lakshdweep'), value: 'Lakshdweep' },
//     { label: t('states.Kerala'), value: 'Kerala' },
//     { label: t('states.Tamil Nadu'), value: 'Tamil Nadu' },
//     { label: t('states.Pondicherry'), value: 'Pondicherry' },
//     { label: t('states.Andaman and Nicobar Islands'), value: 'Andaman and Nicobar Islands' },
//     { label: t('states.Telangana'), value: 'Telangana' },
//     { label: t('states.Andhra Pradesh (New)'), value: 'Andhra Pradesh (New)' }
//   ]

//   const toast = useRef<Toast>(null);

//   useEffect(() => {
//     const fetchParties = async () => {
//       const orgcode = Number(getOrgDataField('orgcode'));
//       setLoading(true);
//       try {
//         const response = await axiosInstance.get(`${OLK_PATH}/parties?orgcode=${orgcode}`);
//         const { olkstatus, olkresult } = response.data;
//         if (olkstatus === 1) {
//           toast.current?.show({ severity: 'error', summary: t('errors.error'), detail: t('errors.somethingWentWrong') });
//         } else if (olkstatus === 0 && olkresult.length === 0) {
//           toast.current?.show({ severity: 'warn', summary: t('warnings.noData'), detail: t('warnings.noPartiesFound') });
//         } else {
//           const formatted = olkresult.map((p: any) => ({
//             custid: p.custid,
//             custname: p.custname,
//             gstin: Object.values(p.gstin)?.[1] || '',
//             custaddr: p.custaddr,
//             state: p.state,
//             pincode: p.pincode,
//           }));
//           setParties(formatted);
//         }
//       } catch (err) {
//         toast.current?.show({ severity: 'error', summary: t('errors.error'), detail: t('errors.failedToFetchParties') });
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchParties();
//   }, [OLK_PATH, t]);

//   return (
//     <div className="grid align-items-center mb-3">
//       <Toast ref={toast} />

//       <div className="flex flex-column md:flex-row justify-between align-items-start md:align-items-center gap-4 w-full">
//         {/* Left Side: Label */}
//         <div className="flex-shrink-0">
//           <h3 className="m-0 text-lg font-semibold whitespace-nowrap">
//             {t('labels.receiverDetails')} | {t('labels.billedTo')} :
//           </h3>
//         </div>

//         {/* Right Side: Dropdown */}
//         <div className="flex flex-column sm:flex-row gap-2 sm:align-items-center w-full md:w-auto">
//           <label htmlFor="party" className="font-medium whitespace-nowrap">
//             {t('labels.selectParty')} :
//           </label>

//           <div className="flex-grow-1">
//             {loading ? (
//               <ProgressSpinner
//                 style={{ width: '1.2rem', height: '1.5rem' }}
//                 strokeWidth="4"
//               />
//             ) : (
//               <Dropdown
//                 id="party"
//                 value={selectedParty}
//                 onChange={(e) => setSelectedParty(e.value)}
//                 options={parties}
//                 optionLabel="custname"
//                 placeholder={t('placeholders.selectParty')}
//                 filter
//                 className="w-full md:w-20rem surface-overlay border-round-md"
//                 panelClassName="custom-dropdown-panel"
//                 itemTemplate={(option) => (
//                   <div title={option.custname} className="text-truncate">
//                     {option.custname}
//                   </div>
//                 )}
//               />
//             )}
//           </div>
//         </div>
//       </div>

//       {selectedParty && (
//         <>
//           <Divider className="my-2" />
//           <div className="grid md:col-6 p">
//             <div className="col-12 md:col-6 py-2">
//               <p className="m-0">
//                 <label htmlFor="gstin">{t('labels.gstin')} : </label>
//                 {selectedParty.gstin || t('labels.na')}
//               </p>
//               <p className="mt-2">
//                 <label htmlFor="address">{t('labels.address')} : </label>
//                 {selectedParty.custaddr}
//               </p>
//             </div>
//             <div className="col-12 md:col-6 py-2">
//               <p className="m-0">
//                 <label htmlFor="state">{t('labels.state')} : </label>
//                 {selectedParty.state}
//               </p>
//               <p className="mt-2">
//                 <label htmlFor="pincode">{t('labels.pincode')} : </label>
//                 {selectedParty.pincode}
//               </p>
//             </div>
//           </div>

//           <Divider />

//           <div className="field-checkbox flex align-items-center gap-2">
//             <Checkbox
//               inputId="sameAsBilling"
//               checked={sameAsBilling}
//               onChange={e => setSameAsBilling(e.checked ?? false)}
//             />
//             <label htmlFor="sameAsBilling" className="font-semibold">
//               {t('labels.sameAsBilling')}
//             </label>
//           </div>

//           {!sameAsBilling && (
//             <div className="grid md:col-6 p">
//               <div className="col-12 md:col-6 py-2">
//                 <label htmlFor="consigneeName">{t('labels.consigneeName')}</label>
//                 <InputText
//                   id="consigneeName"
//                   value={consignee.name}
//                   onChange={(e) => setConsignee(prev => ({ ...prev, name: e.target.value }))}
//                   className="w-full"
//                 />
//               </div>
//               <div className="col-12 md:col-6 py-2">
//                 <label htmlFor="consigneeAddress">{t('labels.consigneeAddress')}</label>
//                 <InputText
//                   id="consigneeAddress"
//                   value={consignee.address}
//                   onChange={(e) => setConsignee(prev => ({ ...prev, address: e.target.value }))}
//                   className="w-full"
//                 />
//               </div>
//               <div className="col-12 md:col-6 py-2">
//                 <label htmlFor="consigneeState">{t('labels.state')}</label>
//                 <Dropdown
//                   id="consigneeState"
//                   options={states}
//                   value={consignee.state}
//                   onChange={(e) => setConsignee(prev => ({ ...prev, state: e.value }))}
//                   placeholder={t('placeholders.selectState')}
//                   className="w-full"
//                 />
//               </div>
//               <div className="col-12 md:col-6 py-2">
//                 <label htmlFor="consigneePincode">{t('labels.pincode')}</label>
//                 <InputText
//                   id="consigneePincode"
//                   value={consignee.pincode}
//                   onChange={(e) => setConsignee(prev => ({ ...prev, pincode: e.target.value }))}
//                   className="w-full"
//                 />
//               </div>
//             </div>
//           )}
//         </>
//       )}
//     </div>
//   );
// }
