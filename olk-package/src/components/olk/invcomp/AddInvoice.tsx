"use client";

import React, { useState, useEffect, useRef,useCallback } from 'react';
import { useForm, FormProvider, Controller, useFieldArray, useFormContext } from 'react-hook-form';
import { Calendar } from 'primereact/calendar';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { Toast } from 'primereact/toast';
import { Dropdown } from 'primereact/dropdown';
import { Checkbox } from 'primereact/checkbox';
import { ProgressSpinner } from 'primereact/progressspinner';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputNumber } from 'primereact/inputnumber';
import { RadioButton } from 'primereact/radiobutton';
import { useSearchParams } from 'next/navigation';
import "../../../styles/olkcss.scss"; 
import { getOrgDataField } from '../../../utils/cookies';
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import { TransportationModeType,InvoicePayload, invoicePayloadSchema } from '../../../schema/olkinvoice';
import { useRouter } from 'next/navigation';
import { Dialog } from 'primereact/dialog';
import AddPartyForm from '../Party/AddParty';
import { useTranslations } from 'next-intl';
import {OlkProvider}  from '../../../contexts/OlkProvider';
import apiCall from '../../../utils/apiCallService';


interface Party {
    custid: number;
    custname: string;
    gstin: string;
    custaddr: string;
    state: string;
    pincode: string;
}

interface Product {
    productcode: number;
    productdesc: string;
    gscode: string;
    gsflag: number;
    prodsp: string;
    prodmrp: string;
    uom_name: string;
    tax: { IGST: number };
    deletable: boolean;
}

interface InvoiceItem {
    id: number;
    product: Product | null;
    hsn: string;
    qty: number;
    freeQty: number;
    amount: number;
    discount: number;
    taxableValue: number;
    gstRate: number;
    gstAmount: number;
    total: number;
}

interface InvoiceFormValues {
    invoiceNo: string;
    invoiceDate: Date;
    partyId: string;
    address: string;
    state: string;
    pincode: string;
    gstin: string;
    paymentMethod: string;
    cashReceived: number;
    bankDetails: string;
    transactionDetails: string;
    transportMode: string;
    vehicleNo: string;
    narration: string;
    dateofsupply: string;
    reverseCharge: string;
    consignee?: {
        name: string;
        address: string;
        state: string;
        pincode: string;
        contact: string;
        email: string;
        gstin: string;
    };
}
interface InvoiceNoResponse {
    olkstatus: number;
    olkresult: string ;
}

interface ApiResponse {
  olkstatus: number;
  olkresult: [];
}

interface invpref{
autoinvno : number; 
}

interface BisDetails {
  bankdetails: any;
  gstin: string;
  orgpan: string;
  orgaddr: string;
  orgpincode: string;
  invoice_preferences : invpref,
}



interface OrgBdtResponse {
  olkstatus: number;
  bisdetails: BisDetails;
  
}

interface InvoiceResponse {
  success: number;
  invid?: number;
  message?: string;
}


const InvoiceForm: React.FC = () => {
    const { OLK_PATH } = useEnvContext();
    const searchParams = useSearchParams();
    const invoiceType = searchParams.get("type");
    const [invoiceNo, setInvoiceNo] = useState('');
    const inoutflag = invoiceType === 'sales' ? 15 : invoiceType === 'purchase' ? 9 : undefined;
    const title =
        invoiceType === "sales"
            ? "Details of Sales Invoice"
            : "Details of Purchase Invoice";
    const toast = useRef<Toast>(null);
    const orgcode = Number(getOrgDataField("orgcode"));
    const orgstate = getOrgDataField("orgstate");
    const isoString1 = getOrgDataField("yearstart");
    const yearstart = isoString1
        ? new Date(isoString1).toISOString().split("T")[0]
        : "";
    const isoString = getOrgDataField("yearend");
    const yearend = isoString
        ? new Date(isoString).toISOString().split("T")[0]
        : "";
    const [searchTerms, setSearchTerms] = useState<{ [key: number]: string }>(
        {}
    );

    // Form setup
    const methods = useForm<
        InvoiceFormValues & { invoiceItems: InvoiceItem[] }
    >({
        defaultValues: {
            invoiceNo: "",
            invoiceDate: new Date(),
            partyId: "",
            address: "",
            state: "",
            pincode: "",
            gstin: "",
            paymentMethod: "Cash",
            cashReceived: 0,
            bankDetails: "",
            transactionDetails: "",
            transportMode: "None",
            vehicleNo: "",
            narration: "",
            reverseCharge: "No",
            dateofsupply: "",
            invoiceItems: [
                {
                    id: Date.now(),
                    product: null,
                    hsn: "",
                    qty: 0.0,
                    freeQty: 0.0,
                    amount: 0.0,
                    discount: 0.0,
                    taxableValue: 0.0,
                    gstRate: 0.0,
                    gstAmount: 0.0,
                    total: 0.0,
                },
            ],
        },
    });

    const {
        handleSubmit,
        reset,
        control,
        setValue,
        watch,
        getValues,
        formState: { errors },
    } = methods;
    const {
        fields: invoiceItemsFields,
        append,
        remove,
        update,
    } = useFieldArray({
        control,
        name: "invoiceItems",
    });

    const paymentMethod = watch("paymentMethod");
    const transportMode = watch("transportMode");
    const invoiceItems = watch("invoiceItems"); // Watch invoiceItems for changes

    // State declarations
    const [parties, setParties] = useState<Party[]>([]);
    const [selectedParty, setSelectedParty] = useState<Party | null>(null);
    const [partyLoading, setPartyLoading] = useState(false);
    const [sameAsBilling, setSameAsBilling] = useState(true);
    const [taxstate, setTaxstate] = useState<string>("Unknown");
    const [sourcestate, setSourcestate] = useState<string>("Unknown");
    const [consignee, setConsignee] = useState({
        name: "",
        address: "",
        state: "",
        pincode: "",
        contact: "",
        email: "",
        gstin: "",
    });
    const [products, setProducts] = useState<Product[]>([]);
    const [discountType, setDiscountType] = useState<"amount" | "percentage">(
        "amount"
    );
    const [totals, setTotals] = useState({
        taxableValue: 0.0,
        gstAmount: 0.0,
        total: 0.0,
        totalDiscount: 0.0,
        totalCess: 0.0,
    });
    const [searchTerm, setSearchTerm] = useState("");
    const [orgstategstin, setOrgstategstin] = useState<string | null>(null);
    const [bankdetails, setBankdetails] = useState<string | null>(null);
    const [productLoading, setProductLoading] = useState(false);
    const [showAddPartyDialog, setShowAddPartyDialog] = useState(false); // State for dialog visibility
    const [orgGstin, setOrgGstin] = useState<string | null>("");
    const [invoiceNoLoading, setInvoiceNoLoading] = useState(false);
    const [autoinv, setAutoinv] = useState<number>(0);

    const router = useRouter();

    const t = useTranslations("ReceiverDetails"); // <-- Using namespace 'ReceiverDetails'

    const states = [
        { label: t("states.Jammu and Kashmir"), value: "Jammu and Kashmir" },
        { label: t("states.Himachal Pradesh"), value: "Himachal Pradesh" },
        { label: t("states.Punjab"), value: "Punjab" },
        { label: t("states.Chandigarh"), value: "Chandigarh" },
        { label: t("states.Uttarakhand"), value: "Uttarakhand" },
        { label: t("states.Haryana"), value: "Haryana" },
        { label: t("states.Delhi"), value: "Delhi" },
        { label: t("states.Rajasthan"), value: "Rajasthan" },
        { label: t("states.Uttar Pradesh"), value: "Uttar Pradesh" },
        { label: t("states.Bihar"), value: "Bihar" },
        { label: t("states.Sikkim"), value: "Sikkim" },
        { label: t("states.Arunachal Pradesh"), value: "Arunachal Pradesh" },
        { label: t("states.Nagaland"), value: "Nagaland" },
        { label: t("states.Manipur"), value: "Manipur" },
        { label: t("states.Mizoram"), value: "Mizoram" },
        { label: t("states.Tripura"), value: "Tripura" },
        { label: t("states.Meghalaya"), value: "Meghalaya" },
        { label: t("states.Assam"), value: "Assam" },
        { label: t("states.West Bengal"), value: "West Bengal" },
        { label: t("states.Jharkhand"), value: "Jharkhand" },
        { label: t("states.Odisha"), value: "Odisha" },
        { label: t("states.Chhattisgarh"), value: "Chhattisgarh" },
        { label: t("states.Madhya Pradesh"), value: "Madhya Pradesh" },
        { label: t("states.Gujarat"), value: "Gujarat" },
        { label: t("states.Daman and Diu"), value: "Daman and Diu" },
        {
            label: t("states.Dadra and Nagar Haveli"),
            value: "Dadra and Nagar Haveli",
        },
        { label: t("states.Maharashtra"), value: "Maharashtra" },
        { label: t("states.Andhra Pradesh"), value: "Andhra Pradesh" },
        { label: t("states.Karnataka"), value: "Karnataka" },
        { label: t("states.Goa"), value: "Goa" },
        { label: t("states.Lakshdweep"), value: "Lakshdweep" },
        { label: t("states.Kerala"), value: "Kerala" },
        { label: t("states.Tamil Nadu"), value: "Tamil Nadu" },
        { label: t("states.Pondicherry"), value: "Pondicherry" },
        {
            label: t("states.Andaman and Nicobar Islands"),
            value: "Andaman and Nicobar Islands",
        },
        { label: t("states.Telangana"), value: "Telangana" },
        {
            label: t("states.Andhra Pradesh (New)"),
            value: "Andhra Pradesh (New)",
        },
    ];

    const paymentOptions = [
        { label: "Cash", value: "Cash" },
        { label: "Bank", value: "Bank" },
        { label: "Online", value: "Online" },
        { label: "On Credit", value: "On Credit" },
    ];

    const paymentModeMap: Record<string, 2 | 3 | 15> = {
        Cash: 3,
        Bank: 2,
        Online: 2,
        "On Credit": 15,
    };

    const transportOptions = [
        { label: "None", value: "None" },
        { label: "By Road", value: "By Road" },
        { label: "Ship", value: "Ship" },
        { label: "Rail", value: "Rail" },
        { label: "Air", value: "Air" },
        { label: "Other", value: "Other" },
    ];

    interface BisDetails {
        gstin: string | null;
        orgpan: string | null;
        bankdetails: string | null;
    }

// Fetch Organization Details
    useEffect(() => {
        const fetchOrgDetails = async () => {
            try {
                const response = await apiCall<OrgBdtResponse>("GET",`${OLK_PATH}/organisations/bdt?orgcode=${orgcode}`);
                const { olkstatus, bisdetails } = response.data;
                if (olkstatus === 0) {
                    setOrgGstin(bisdetails.gstin || null);
                    const bankDetails = bisdetails.bankdetails || null;
                    const autoInv = bisdetails.invoice_preferences.autoinvno || 0;
                    setBankdetails(bankDetails);
                    setAutoinv(autoInv);
                    if (autoInv == 1) {
                        await fetchInvoiceNumber();
                    }
                } else {
                    toast.current?.show({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Failed to fetch organization details',
                        life: 3000,
                    });
                }
            } catch (err) {
                toast.current?.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to fetch organization details',
                    life: 3000,
                });
            }
        };

        if (orgcode) {
            fetchOrgDetails();
        }
    }, [orgcode, OLK_PATH, setValue]);



 // Fetch Invoice Number
    const fetchInvoiceNumber = async () => {
        if (!orgcode) return;
        if (!inoutflag) {
        setInvoiceNo(''); // Clear invoice number if inoutflag is invalid
        return;
      }
        setInvoiceNoLoading(true);
        try {
            const financialYear = yearstart ? yearstart.slice(0, 4) + (yearend.slice(-2)) : "";
            const icflag = 9;
            const response = await apiCall<InvoiceNoResponse>(
                "GET",
                `${OLK_PATH}/invoice/invoiceno?orgcode=${orgcode}&financialyear=${financialYear}&inoutflag=${inoutflag}&icflag=${icflag}`
            );
            const { olkstatus, olkresult } = response.data;
            if (olkstatus === 0) {
                const invoiceNo = olkresult;
                setValue("invoiceNo", invoiceNo );
            } else {
                toast.current?.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to fetch invoice number',
                    life: 3000,
                });
            }
        } catch (err) {
            console.error('Fetch Invoice Number Error:', err);
            toast.current?.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to fetch invoice number',
                life: 3000,
            });
        } finally {
            setInvoiceNoLoading(false);
        }
    };
    
useEffect(() => {
    fetchInvoiceNumber();
  }, [orgcode, inoutflag]);

  // Fetch Parties
  const fetchParties = async () => {
    setPartyLoading(true);
    try {
      const response = await apiCall<ApiResponse>("GET", `${OLK_PATH}/parties?orgcode=${orgcode}`);
      const { olkstatus, olkresult } = response.data;
      if (olkstatus === 1) {
        toast.current?.show({ severity: 'error', summary: 'Error', detail: 'Something went wrong' });
      } else if (olkstatus === 0 && (!olkresult || Object.keys(olkresult).length === 0)) {
        toast.current?.show({ severity: 'warn', summary: 'No Data', detail: 'No parties found' });
      } else {
        const olkresultArray = Array.isArray(olkresult) ? olkresult : Object.values(olkresult);
        const formatted = olkresultArray.map((p: any) => {
          const gstinValue = typeof p.gstin === 'object' && p.gstin !== null
            ? Object.values(p.gstin)[0] || ''
            : p.gstin || '';
          return {
            custid: p.custid || p.customerId || 0,
            custname: p.custname || p.customerName || '',
            gstin: gstinValue,
            custaddr: p.custaddr || p.address || '',
            state: p.state || '',
            pincode: p.pincode || '',
          };
        });
        setParties(formatted);
      }
    } catch (err) {
      console.error('Fetch Parties Error:', err);
      toast.current?.show({ severity: 'error', summary: 'Error', detail: 'Failed to fetch parties' });
    } finally {
      setPartyLoading(false);
    }
  };

    useEffect(() => {
        fetchParties();
    }, [orgcode, OLK_PATH]);

    useEffect(() => {
        if (
            orgGstin === null &&
            (!selectedParty || selectedParty.gstin === "")
        ) {
            setValue("reverseCharge", "No", { shouldValidate: true });
        }
    }, [orgGstin, selectedParty, setValue]);

    useEffect(() => {
        const newTaxstate =
            invoiceType === "sales"
                ? selectedParty?.state || "Unknown"
                : orgstate || "Unknown";
        const newSourcestate =
            invoiceType === "sales"
                ? orgstate || "Unknown"
                : selectedParty?.state || "Unknown";
        setTaxstate(newTaxstate);
        setSourcestate(newSourcestate);
    }, [selectedParty, invoiceType, orgstate]);

  // Fetch Products
  useEffect(() => {
    if (!orgcode) return;
    const fetchProducts = async () => {
      setProductLoading(true);
      try {
        const response = await apiCall<ApiResponse>("GET", `${OLK_PATH}/products?orgcode=${orgcode}`);
        if (response.data.olkstatus === 1) {
          toast.current?.show({ severity: 'error', summary: 'Error', detail: 'Something went wrong' });
        } else if (response.data.olkstatus === 0 && response.data.olkresult.length === 0) {
          toast.current?.show({ severity: 'info', summary: 'No Data', detail: 'No products found' });
        } else {
          setProducts(response.data.olkresult);
        }
      } catch (error) {
        toast.current?.show({ severity: 'error', summary: 'API Error', detail: 'Failed to fetch products' });
      } finally {
        setProductLoading(false);
      }
    };
    fetchProducts();
  }, [orgcode, OLK_PATH]);

    // Validate invoice item
    const validateItem = (item: InvoiceItem) => {
        const errors: { [key: string]: string } = {};
        if (!item.amount || item.amount <= 0) {
            errors.amount =
                "Amount must be a positive number with up to 3 decimal places";
        } else if (!/^\d*\.?\d{0,3}$/.test(item.amount.toString())) {
            errors.amount = "Amount must have up to 3 decimal places";
        }
        if (item.product?.gsflag === 7 && (!item.qty || item.qty <= 0)) {
            errors.qty =
                "Quantity must be a positive number with up to 3 decimal places";
        } else if (
            item.product?.gsflag === 7 &&
            !/^\d*\.?\d{0,3}$/.test(item.qty.toString())
        ) {
            errors.qty = "Quantity must have up to 3 decimal places";
        }
        if (item.freeQty < 0) {
            errors.freeQty =
                "Free Quantity must be non-negative with up to 3 decimal places";
        } else if (!/^\d*\.?\d{0,3}$/.test(item.freeQty.toString())) {
            errors.freeQty = "Free Quantity must have up to 3 decimal places";
        }
        if (discountType === "percentage" && item.discount > 100) {
            errors.discount = "Percentage discount cannot exceed 100%";
        } else if (
            discountType === "percentage" &&
            !/^\d*\.?\d{0,2}$/.test(item.discount.toString())
        ) {
            errors.discount =
                "Percentage discount must have up to 2 decimal places";
        } else if (
            discountType === "amount" &&
            !/^\d*\.?\d{0,3}$/.test(item.discount.toString())
        ) {
            errors.discount =
                "Amount discount must have up to 3 decimal places";
        }
        return errors;
    };

    // Add new row with limit check
    const addNewRow = () => {
        if (invoiceItemsFields.length >= products.length) {
            toast.current?.show({
                severity: "warn",
                summary: "Limit Reached",
                detail: `Cannot add more rows. Only ${products.length} products are available.`,
                life: 3000,
            });
            return;
        }
        const newItem = {
            id: Date.now(),
            product: null,
            hsn: "",
            qty: 0.0,
            freeQty: 0.0,
            amount: 0.0,
            discount: 0.0,
            taxableValue: 0.0,
            gstRate: 0.0,
            gstAmount: 0.0,
            total: 0.0,
        };
        append(newItem);
    };

    // Delete row
    const deleteRow = (index: number) => {
        remove(index);
        if (invoiceItemsFields.length === 0) {
            addNewRow();
        }
    };

    //   const updateInvoiceItem = (index: number, field: keyof InvoiceItem, value: any) => {
    //   // Update the changed field
    //   setValue(`invoiceItems.${index}.${field}`, value, { shouldValidate: true, shouldDirty: true, shouldTouch: true });

    //   // Get the current item state from the form
    //   const currentItem = getValues(`invoiceItems.${index}`);

    //   // Prepare updated values, handling null product
    //   let updatedGstRate = currentItem.gstRate || 0.00;
    //   let updatedHsn = currentItem.hsn || '';
    //   let updatedAmount = currentItem.amount || 0.00;
    //   let updatedQty = currentItem.qty || 0.00;

    //   if (field === 'product' && value) {
    //     updatedHsn = value.gscode || '';
    //     // Set GST rate to 0.00 if orgGstin is empty/null and invoice is sales
    //     updatedGstRate = (!orgGstin && invoiceType === 'sales') ? 0.00 : (value.tax?.IGST ?? 0.00);
    //     updatedAmount = parseFloat(value.prodsp) || 0.00;
    //     // For services, set qty to 1; for products, reset qty to 0 if not set
    //     updatedQty = value.gsflag === 19 ? 1 : (currentItem.qty || 0.00);
    //     setValue(`invoiceItems.${index}.hsn`, updatedHsn, { shouldValidate: true, shouldDirty: true, shouldTouch: true });
    //     setValue(`invoiceItems.${index}.gstRate`, updatedGstRate, { shouldValidate: true, shouldDirty: true, shouldTouch: true });
    //     setValue(`invoiceItems.${index}.amount`, updatedAmount, { shouldValidate: true, shouldDirty: true, shouldTouch: true });
    //     setValue(`invoiceItems.${index}.qty`, updatedQty, { shouldValidate: true, shouldDirty: true, shouldTouch: true });
    //   }

    //   // Recalculate dependent fields
    //   const qty = field === 'qty' ? value || 0 : updatedQty;
    //   const amount = field === 'amount' ? value || 0 : updatedAmount;
    //   const discount = field === 'discount' ? value || 0 : currentItem.discount || 0;
    //   const gstRate = field === 'product' ? updatedGstRate : currentItem.gstRate || 0;
    //   const isService = currentItem.product?.gsflag === 19;

    //   let taxableValue = 0.00;
    //   let effectiveDiscount = 0.00;

    //   if (isService) {
    //     // For services: taxableValue = amount - discount (or amount - (amount * discount / 100) if percentage)
    //     effectiveDiscount = discountType === 'percentage' ? (amount * discount) / 100 : discount;
    //     taxableValue = amount - effectiveDiscount;
    //   } else {
    //     // For products: taxableValue = (amount * qty) - discount (or (amount * qty) * discount / 100 if percentage)
    //     effectiveDiscount = discountType === 'percentage' ? (amount * qty * discount) / 100 : discount;
    //     taxableValue = (amount * qty) - effectiveDiscount;
    //   }

    //   const gstAmount = (taxableValue * gstRate) / 100;
    //   const total = taxableValue + gstAmount;

    //   // Update dependent fields
    //   setValue(`invoiceItems.${index}.taxableValue`, parseFloat(taxableValue.toFixed(2)), { shouldValidate: true, shouldDirty: true, shouldTouch: true });
    //   setValue(`invoiceItems.${index}.gstAmount`, parseFloat(gstAmount.toFixed(2)), { shouldValidate: true, shouldDirty: true, shouldTouch: true });
    //   setValue(`invoiceItems.${index}.total`, parseFloat(total.toFixed(2)), { shouldValidate: true, shouldDirty: true, shouldTouch: true });

    //   // Update the field array directly to ensure DataTable re-renders
    //   const updatedItem = {
    //     ...currentItem,
    //     [field]: value,
    //     hsn: field === 'product' ? updatedHsn : currentItem.hsn,
    //     gstRate: field === 'product' ? updatedGstRate : currentItem.gstRate,
    //     amount: field === 'product' ? updatedAmount : currentItem.amount,
    //     qty: field === 'product' ? updatedQty : qty,
    //     taxableValue: parseFloat(taxableValue.toFixed(2)),
    //     gstAmount: parseFloat(gstAmount.toFixed(2)),
    //     total: parseFloat(total.toFixed(2)),
    //   };
    //   update(index, updatedItem);

    //   // Debugging log
    //   console.log(`Updated item ${index}:`, {
    //     field,
    //     value,
    //     qty,
    //     amount,
    //     discount,
    //     gstRate,
    //     taxableValue,
    //     gstAmount,
    //     total,
    //     isService,
    //   });
    // };

    const reverseCharge = watch("reverseCharge");
    const updateInvoiceItem = (
        index: number,
        field: keyof InvoiceItem,
        value: any
    ) => {
        // Update the changed field
        setValue(`invoiceItems.${index}.${field}`, value, {
            shouldValidate: true,
            shouldDirty: true,
            shouldTouch: true,
        });

        // Get the current item state from the form
        const currentItem = getValues(`invoiceItems.${index}`);
        const reverseCharge = getValues("reverseCharge"); // Get reverseCharge value

        // Prepare updated values, handling null product
        let updatedGstRate = currentItem.gstRate || 0.0;
        let updatedHsn = currentItem.hsn || "";
        let updatedAmount = currentItem.amount || 0.0;
        let updatedQty = currentItem.qty || 0.0;

        if (field === "product" && value) {
            updatedHsn = value.gscode || "";
            updatedAmount = parseFloat(value.prodsp) || 0.0;
            updatedQty = value.gsflag === 19 ? 1 : currentItem.qty || 0.0;

            // GST rate logic for sales invoice
            if (invoiceType === "sales") {
                if (orgGstin === null) {
                    if (selectedParty?.gstin && reverseCharge === "Yes") {
                        // Case 1: orgGstin is null, selectedParty.gstin is not null, reverseCharge is Yes
                        updatedGstRate = value.tax?.IGST ?? 0.0;
                    } else {
                        // Case 1: reverseCharge is No, or Case 2: selectedParty.gstin is null
                        updatedGstRate = 0.0;
                    }
                } else {
                    // Default case when orgGstin is not null
                    updatedGstRate = value.tax?.IGST ?? 0.0;
                }
            } else {
                // For purchase invoice, use product GST rate
                updatedGstRate = value.tax?.IGST ?? 0.0;
            }

            if (invoiceType === "purchase") {
                if (
                    orgGstin === null &&
                    (!selectedParty || selectedParty.gstin === "")
                ) {
                    updatedGstRate = 0.0;
                } else if (
                    orgGstin !== null &&
                    (!selectedParty || selectedParty.gstin === "")
                ) {
                    updatedGstRate =
                        reverseCharge === "Yes" ? value.tax?.IGST ?? 0.0 : 0.0;
                } else {
                    updatedGstRate = value.tax?.IGST ?? 0.0;
                }
            }

            setValue(`invoiceItems.${index}.hsn`, updatedHsn, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
            });
            setValue(`invoiceItems.${index}.gstRate`, updatedGstRate, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
            });
            setValue(`invoiceItems.${index}.amount`, updatedAmount, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
            });
            setValue(`invoiceItems.${index}.qty`, updatedQty, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
            });
        }

        // Recalculate dependent fields
        const qty = field === "qty" ? value || 0 : updatedQty;
        const amount = field === "amount" ? value || 0 : updatedAmount;
        const discount =
            field === "discount" ? value || 0 : currentItem.discount || 0;
        const gstRate =
            field === "product" ? updatedGstRate : currentItem.gstRate || 0;
        const isService = currentItem.product?.gsflag === 19;

        let taxableValue = 0.0;
        let effectiveDiscount = 0.0;

        if (isService) {
            effectiveDiscount =
                discountType === "percentage"
                    ? (amount * discount) / 100
                    : discount;
            taxableValue = amount - effectiveDiscount;
        } else {
            effectiveDiscount =
                discountType === "percentage"
                    ? (amount * qty * discount) / 100
                    : discount;
            taxableValue = amount * qty - effectiveDiscount;
        }

        const gstAmount = (taxableValue * gstRate) / 100;
        const total = taxableValue + gstAmount;

        // Update dependent fields
        setValue(
            `invoiceItems.${index}.taxableValue`,
            parseFloat(taxableValue.toFixed(2)),
            { shouldValidate: true, shouldDirty: true, shouldTouch: true }
        );
        setValue(
            `invoiceItems.${index}.gstAmount`,
            parseFloat(gstAmount.toFixed(2)),
            { shouldValidate: true, shouldDirty: true, shouldTouch: true }
        );
        setValue(`invoiceItems.${index}.total`, parseFloat(total.toFixed(2)), {
            shouldValidate: true,
            shouldDirty: true,
            shouldTouch: true,
        });

        // Update the field array directly to ensure DataTable re-renders
        const updatedItem = {
            ...currentItem,
            [field]: value,
            hsn: field === "product" ? updatedHsn : currentItem.hsn,
            gstRate: parseFloat(gstRate.toFixed(2)),
            amount: field === "product" ? updatedAmount : currentItem.amount,
            qty: field === "product" ? updatedQty : qty,
            taxableValue: parseFloat(taxableValue.toFixed(2)),
            gstAmount: parseFloat(gstAmount.toFixed(2)),
            total: parseFloat(total.toFixed(2)),
        };
        update(index, updatedItem);

        // Debugging log
        console.log(`Updated item ${index}:`, {
            field,
            value,
            qty,
            amount,
            discount,
            gstRate,
            taxableValue,
            gstAmount,
            total,
            isService,
            reverseCharge,
        });
    };

    useEffect(() => {
        if (
            (invoiceType === "sales" &&
                orgGstin === null &&
                selectedParty?.gstin) ||
            (invoiceType === "purchase" &&
                orgGstin !== null &&
                (!selectedParty || selectedParty.gstin === ""))
        ) {
            const reverseChargeValue = getValues("reverseCharge");
            invoiceItemsFields.forEach((item, index) => {
                if (item.product) {
                    const newGstRate =
                        reverseChargeValue === "Yes"
                            ? item.product.tax?.IGST ?? 0.0
                            : 0.0;
                    if (item.gstRate !== newGstRate) {
                        setValue(`invoiceItems.${index}.gstRate`, newGstRate, {
                            shouldValidate: true,
                        });
                        const currentItem = getValues(`invoiceItems.${index}`);
                        const qty = currentItem.qty || 0.0;
                        const amount = currentItem.amount || 0.0;
                        const discount = currentItem.discount || 0.0;
                        const isService = currentItem.product?.gsflag === 19;

                        let taxableValue = 0.0;
                        let effectiveDiscount = 0.0;

                        if (isService) {
                            effectiveDiscount =
                                discountType === "percentage"
                                    ? (amount * discount) / 100
                                    : discount;
                            taxableValue = amount - effectiveDiscount;
                        } else {
                            effectiveDiscount =
                                discountType === "percentage"
                                    ? (amount * qty * discount) / 100
                                    : discount;
                            taxableValue = amount * qty - effectiveDiscount;
                        }

                        const gstAmount = (taxableValue * newGstRate) / 100;
                        const total = taxableValue + gstAmount;

                        setValue(
                            `invoiceItems.${index}.taxableValue`,
                            parseFloat(taxableValue.toFixed(2)),
                            { shouldValidate: true }
                        );
                        setValue(
                            `invoiceItems.${index}.gstAmount`,
                            parseFloat(gstAmount.toFixed(2)),
                            { shouldValidate: true }
                        );
                        setValue(
                            `invoiceItems.${index}.total`,
                            parseFloat(total.toFixed(2)),
                            { shouldValidate: true }
                        );

                        update(index, {
                            ...currentItem,
                            gstRate: parseFloat(newGstRate.toFixed(2)),
                            taxableValue: parseFloat(taxableValue.toFixed(2)),
                            gstAmount: parseFloat(gstAmount.toFixed(2)),
                            total: parseFloat(total.toFixed(2)),
                        });
                    }
                }
            });
        }
    }, [
        reverseCharge,
        orgGstin,
        selectedParty?.gstin,
        invoiceType,
        getValues,
        setValue,
        update,
        discountType,
        invoiceItemsFields,
    ]);

    // useEffect(() => {
    //     if (invoiceType === 'sales' && orgGstin === null && selectedParty?.gstin) {
    //       const reverseChargeValue = getValues('reverseCharge');
    //       invoiceItemsFields.forEach((item, index) => {
    //         if (item.product) {
    //           const newGstRate = reverseChargeValue === 'Yes' ? (item.product.tax?.IGST ?? 0.00) : 0.00;
    //           if (item.gstRate !== newGstRate) {
    //             setValue(`invoiceItems.${index}.gstRate`, newGstRate, { shouldValidate: true });
    //             const currentItem = getValues(`invoiceItems.${index}`);
    //             const qty = currentItem.qty || 0.00;
    //             const amount = currentItem.amount || 0.00;
    //             const discount = currentItem.discount || 0.00;
    //             const isService = currentItem.product?.gsflag === 19;

    //             let taxableValue = 0.00;
    //             let effectiveDiscount = 0.00;

    //             if (isService) {
    //               effectiveDiscount = discountType === 'percentage' ? (amount * discount) / 100 : discount;
    //               taxableValue = amount - effectiveDiscount;
    //             } else {
    //               effectiveDiscount = discountType === 'percentage' ? (amount * qty * discount) / 100 : discount;
    //               taxableValue = (amount * qty) - effectiveDiscount;
    //             }

    //             const gstAmount = (taxableValue * newGstRate) / 100;
    //             const total = taxableValue + gstAmount;

    //             setValue(`invoiceItems.${index}.taxableValue`, parseFloat(taxableValue.toFixed(2)), { shouldValidate: true });
    //             setValue(`invoiceItems.${index}.gstAmount`, parseFloat(gstAmount.toFixed(2)), { shouldValidate: true });
    //             setValue(`invoiceItems.${index}.total`, parseFloat(total.toFixed(2)), { shouldValidate: true });

    //             update(index, {
    //               ...currentItem,
    //               gstRate: parseFloat(newGstRate.toFixed(2)),
    //               taxableValue: parseFloat(taxableValue.toFixed(2)),
    //               gstAmount: parseFloat(gstAmount.toFixed(2)),
    //               total: parseFloat(total.toFixed(2)),
    //             });
    //           }
    //         }
    //       });
    //     }
    //   }, [reverseCharge, orgGstin, selectedParty?.gstin, invoiceType, getValues, setValue, update, discountType]);

    useEffect(() => {
        if (
            (orgGstin === null &&
                (!selectedParty || selectedParty.gstin === "")) || // Both sales and purchase
            (invoiceType === "sales" && orgGstin !== null) // Sales with orgGstin
        ) {
            setValue("reverseCharge", "No", { shouldValidate: true });
        }
    }, [orgGstin, selectedParty, invoiceType, setValue]);

    // useEffect(() => {
    //   if (invoiceType === 'sales' && orgGstin === null && selectedParty?.gstin) {
    //     const reverseCharge = getValues('reverseCharge');
    //     invoiceItemsFields.forEach((item, index) => {
    //       if (item.product) {
    //         const newGstRate = reverseCharge === 'Yes' ? (item.product.tax?.IGST ?? 0.00) : 0.00;
    //         updateInvoiceItem(index, 'gstRate', newGstRate);
    //       }
    //     });
    //   }
    // }, [watch('reverseCharge'), orgGstin, selectedParty, invoiceItemsFields, updateInvoiceItem, invoiceType, getValues]);

    // Calculate totals
    const calculateTotals = () => {
        const totals = invoiceItemsFields.reduce(
            (acc, item) => {
                const isService = item.product?.gsflag === 19;
                const discount =
                    discountType === "percentage"
                        ? isService
                            ? (item.amount * item.discount) / 100
                            : (item.amount * item.qty * item.discount) / 100
                        : item.discount;
                const gstAmount =
                    !orgGstin && invoiceType === "sales" ? 0.0 : item.gstAmount;
                return {
                    taxableValue: acc.taxableValue + item.taxableValue,
                    gstAmount: acc.gstAmount + gstAmount,
                    total: acc.total + (item.taxableValue + gstAmount),
                    totalDiscount: acc.totalDiscount + discount,
                };
            },
            {
                taxableValue: 0.0,
                gstAmount: 0.0,
                total: 0.0,
                totalDiscount: 0.0,
            }
        );
        return {
            taxableValue: parseFloat(totals.taxableValue.toFixed(2)),
            gstAmount: parseFloat(totals.gstAmount.toFixed(2)),
            total: parseFloat(totals.total.toFixed(2)),
            totalDiscount: parseFloat(totals.totalDiscount.toFixed(2)),
            totalCess: 0.0,
        };
    };

    useEffect(() => {
        const newTotals = calculateTotals();
        setTotals(newTotals);
    }, [invoiceItemsFields, discountType]);

    // Watch invoiceItems and update dependent fields reactively
    //  useEffect(() => {
    //   invoiceItems.forEach((item, index) => {
    //     const qty = item.qty || 0.00;
    //     const amount = item.amount || 0.00;
    //     const discount = item.discount || 0.00;
    //     // Set GST rate to 0.00 if orgGstin is empty/null and invoice is sales
    //     const gstRate = (!orgGstin && invoiceType === 'sales') ? 0.00 : (item.gstRate || 0.00);
    //     const isService = item.product?.gsflag === 19;

    //     let taxableValue = 0.00;
    //     let effectiveDiscount = 0.00;

    //     if (isService) {
    //       effectiveDiscount = discountType === 'percentage' ? (amount * discount) / 100 : discount;
    //       taxableValue = amount - effectiveDiscount;
    //     } else {
    //       effectiveDiscount = discountType === 'percentage' ? (amount * qty * discount) / 100 : discount;
    //       taxableValue = (amount * qty) - effectiveDiscount;
    //     }

    //     const gstAmount = (taxableValue * gstRate) / 100;
    //     const total = taxableValue + gstAmount;

    //     // Only update if values have changed to prevent infinite loops
    //     if (
    //       item.taxableValue !== parseFloat(taxableValue.toFixed(2)) ||
    //       item.gstRate !== parseFloat(gstRate.toFixed(2)) ||
    //       item.gstAmount !== parseFloat(gstAmount.toFixed(2)) ||
    //       item.total !== parseFloat(total.toFixed(2))
    //     ) {
    //       setValue(`invoiceItems.${index}.gstRate`, parseFloat(gstRate.toFixed(2)), { shouldValidate: true, shouldDirty: true, shouldTouch: true });
    //       setValue(`invoiceItems.${index}.taxableValue`, parseFloat(taxableValue.toFixed(2)), { shouldValidate: true, shouldDirty: true, shouldTouch: true });
    //       setValue(`invoiceItems.${index}.gstAmount`, parseFloat(gstAmount.toFixed(2)), { shouldValidate: true, shouldDirty: true, shouldTouch: true });
    //       setValue(`invoiceItems.${index}.total`, parseFloat(total.toFixed(2)), { shouldValidate: true, shouldDirty: true, shouldTouch: true });

    //       // Update the field array to ensure DataTable reflects the changes
    //       update(index, {
    //         ...item,
    //         gstRate: parseFloat(gstRate.toFixed(2)),
    //         taxableValue: parseFloat(taxableValue.toFixed(2)),
    //         gstAmount: parseFloat(gstAmount.toFixed(2)),
    //         total: parseFloat(total.toFixed(2)),
    //       });
    //     }
    //   });
    // }, [invoiceItems, discountType, setValue, update, orgGstin, invoiceType]);

    const previousInvoiceItems = useRef(invoiceItems);

    useEffect(() => {
        invoiceItems.forEach((item, index) => {
            const prevItem = previousInvoiceItems.current[index];
            if (
                prevItem &&
                (item.qty !== prevItem.qty ||
                    item.amount !== prevItem.amount ||
                    item.discount !== prevItem.discount ||
                    item.product?.productcode !== prevItem.product?.productcode)
            ) {
                const qty = item.qty || 0.0;
                const amount = item.amount || 0.0;
                const discount = item.discount || 0.0;
                const gstRate =
                    !orgGstin && invoiceType === "sales"
                        ? 0.0
                        : item.gstRate || 0.0;
                const isService = item.product?.gsflag === 19;

                let taxableValue = 0.0;
                let effectiveDiscount = 0.0;

                if (isService) {
                    effectiveDiscount =
                        discountType === "percentage"
                            ? (amount * discount) / 100
                            : discount;
                    taxableValue = amount - effectiveDiscount;
                } else {
                    effectiveDiscount =
                        discountType === "percentage"
                            ? (amount * qty * discount) / 100
                            : discount;
                    taxableValue = amount * qty - effectiveDiscount;
                }

                const gstAmount = (taxableValue * gstRate) / 100;
                const total = taxableValue + gstAmount;

                if (
                    item.taxableValue !== parseFloat(taxableValue.toFixed(2)) ||
                    item.gstRate !== parseFloat(gstRate.toFixed(2)) ||
                    item.gstAmount !== parseFloat(gstAmount.toFixed(2)) ||
                    item.total !== parseFloat(total.toFixed(2))
                ) {
                    setValue(
                        `invoiceItems.${index}.gstRate`,
                        parseFloat(gstRate.toFixed(2)),
                        { shouldValidate: true }
                    );
                    setValue(
                        `invoiceItems.${index}.taxableValue`,
                        parseFloat(taxableValue.toFixed(2)),
                        { shouldValidate: true }
                    );
                    setValue(
                        `invoiceItems.${index}.gstAmount`,
                        parseFloat(gstAmount.toFixed(2)),
                        { shouldValidate: true }
                    );
                    setValue(
                        `invoiceItems.${index}.total`,
                        parseFloat(total.toFixed(2)),
                        { shouldValidate: true }
                    );

                    update(index, {
                        ...item,
                        gstRate: parseFloat(gstRate.toFixed(2)),
                        taxableValue: parseFloat(taxableValue.toFixed(2)),
                        gstAmount: parseFloat(gstAmount.toFixed(2)),
                        total: parseFloat(total.toFixed(2)),
                    });
                }
            }
        });
        previousInvoiceItems.current = invoiceItems;
    }, [invoiceItems, discountType, setValue, update, orgGstin, invoiceType]);

    const updateSearchTerm = (itemId: number, term: string) => {
        setSearchTerms((prev) => ({ ...prev, [itemId]: term }));
    };

    // Get available products
    const getAvailableProducts = useCallback(
        (currentItemId: number) => {
            const selectedProductCodes = invoiceItemsFields
                .filter(
                    (item) => item.id !== currentItemId && item.product !== null
                )
                .map((item) => item.product!.productcode);
            const searchTerm = searchTerms[currentItemId] || "";
            return products
                .filter(
                    (product) =>
                        !selectedProductCodes.includes(product.productcode)
                )
                .filter((product) =>
                    product.productdesc
                        .toLowerCase()
                        .includes(searchTerm.toLowerCase())
                );
        },
        [invoiceItemsFields, products, searchTerms]
    );

    // Handle Party Selection
    const handlePartySelect = (party: Party | null) => {
        setSelectedParty(party);
        if (party) {
            setValue("partyId", String(party.custid));
            setValue("address", party.custaddr);
            setValue("state", party.state);
            setValue("pincode", party.pincode);
            setValue("gstin", party.gstin);
        } else {
            setValue("partyId", "");
            setValue("address", "");
            setValue("state", "");
            setValue("pincode", "");
            setValue("gstin", "");
        }
    };

    // Handle successful party addition
    const handlePartyAdded = () => {
        setShowAddPartyDialog(false); // Close the dialog
        fetchParties(); // Refresh the parties list
    };

    useEffect(() => {
        if (sameAsBilling) {
            setConsignee({
                name: "",
                address: "",
                state: "",
                pincode: "",
                contact: "",
                email: "",
                gstin: "",
            });
        }
    }, [sameAsBilling]);

    const numberToWords = (num: number): string => {
        const ones = [
            "",
            "One",
            "Two",
            "Three",
            "Four",
            "Five",
            "Six",
            "Seven",
            "Eight",
            "Nine",
            "Ten",
            "Eleven",
            "Twelve",
            "Thirteen",
            "Fourteen",
            "Fifteen",
            "Sixteen",
            "Seventeen",
            "Eighteen",
            "Nineteen",
        ];
        const tens = [
            "",
            "",
            "Twenty",
            "Thirty",
            "Forty",
            "Fifty",
            "Sixty",
            "Seventy",
            "Eighty",
            "Ninety",
        ];
        const thousands = ["", "Thousand", "Lakh", "Crore"];

        const convertLessThanThousand = (num: number): string => {
            if (num === 0) return "";
            if (num < 20) return ones[num];
            if (num < 100) {
                const ten = Math.floor(num / 10);
                const rest = num % 10;
                return tens[ten] + (rest ? " " + ones[rest] : "");
            }
            const hundred = Math.floor(num / 100);
            const rest = num % 100;
            return (
                ones[hundred] +
                " Hundred" +
                (rest ? " " + convertLessThanThousand(rest) : "")
            );
        };

        if (num === 0) return "Zero";

        const [integerPart, decimalPart] = num
            .toFixed(2)
            .split(".")
            .map(Number);

        let words = "";
        let integer = integerPart;
        let thousandIndex = 0;

        if (integer > 0) {
            const thousandChunk = integer % 1000;
            if (thousandChunk) {
                words =
                    convertLessThanThousand(thousandChunk) +
                    (thousands[0] ? " " + thousands[0] : "");
            }
            integer = Math.floor(integer / 1000);
            thousandIndex++;
        }

        while (integer > 0) {
            const chunk = integer % 100;
            if (chunk) {
                const chunkWords = convertLessThanThousand(chunk);
                words =
                    chunkWords +
                    (thousands[thousandIndex]
                        ? " " + thousands[thousandIndex]
                        : "") +
                    (words ? " " + words : "");
            }
            integer = Math.floor(integer / 100);
            thousandIndex++;
        }

        words = words ? words + " Rupees" : "";

        if (decimalPart > 0) {
            const paiseWords = convertLessThanThousand(decimalPart);
            words += (words ? " and " : "") + paiseWords + " Paise";
        }

        return words || "Zero Rupees";
    };

    // Body templates
    //   const productBodyTemplate = (rowData: InvoiceItem, { rowIndex }: { rowIndex: number }) => (
    //     <Controller
    //       name={`invoiceItems.${rowIndex}.product`}
    //       control={control}
    //       render={({ field }) => (
    //         <Dropdown
    //           value={field.value}
    //           options={getAvailableProducts(rowData.id)}
    //           optionLabel="productdesc"
    //           placeholder="Select Product"
    //           filter
    //           onChange={(e) => {
    //             field.onChange(e.value);
    //             updateInvoiceItem(rowIndex, 'product', e.value);
    //           }}
    //           onFilter={(e) => setSearchTerm(e.filter)}
    //           className="w-full"
    //         />
    //       )}
    //     />
    //   );

    const productBodyTemplate = (
        rowData: InvoiceItem,
        { rowIndex }: { rowIndex: number }
    ) => (
        <Controller
            name={`invoiceItems.${rowIndex}.product`}
            control={control}
            render={({ field }) => (
                <Dropdown
                    value={field.value}
                    options={getAvailableProducts(rowData.id)}
                    optionLabel="productdesc"
                    placeholder="Select Product"
                    filter
                    onChange={(e) => {
                        field.onChange(e.value);
                        updateInvoiceItem(rowIndex, "product", e.value);
                    }}
                    onFilter={(e) => updateSearchTerm(rowData.id, e.filter)}
                    className="w-full"
                />
            )}
        />
    );

    const hsnBodyTemplate = (rowData: InvoiceItem) => (
        <InputText value={rowData.hsn} disabled className="w-full" />
    );

    const qtyBodyTemplate = (
        rowData: InvoiceItem,
        { rowIndex }: { rowIndex: number }
    ) => (
        <Controller
            name={`invoiceItems.${rowIndex}.qty`}
            control={control}
            rules={{
                required:"Please enter the quantity.",
                min:{
                    value:0.001,
                    message: "Qty must be greater than 0.",

                }
            }}
            render={({ field, fieldState }) => (
                <InputNumber
                    value={field.value}
                    onValueChange={(e) => {
                        field.onChange(e.value);
                        updateInvoiceItem(rowIndex, "qty", e.value || 0.0);
                    }}
                    placeholder="0.000"
                    className={`w-full ${
                        fieldState.invalid ? "p-invalid" : ""
                    }`}
                    inputStyle={{ textAlign: "right" }}
                    disabled={rowData.product?.gsflag === 19}
                    min={0}
                    mode="decimal"
                    minFractionDigits={3}
                    maxFractionDigits={3}
                    useGrouping={false}
                />
            )}
        />
    );

    const freeQtyBodyTemplate = (
        rowData: InvoiceItem,
        { rowIndex }: { rowIndex: number }
    ) => (
        <Controller
            name={`invoiceItems.${rowIndex}.freeQty`}
            control={control}
            render={({ field }) => (
                <InputNumber
                    value={field.value}
                    onValueChange={(e) => {
                        field.onChange(e.value);
                        updateInvoiceItem(rowIndex, "freeQty", e.value || 0.0);
                    }}
                    placeholder="0.000"
                    className="w-full"
                    inputStyle={{ textAlign: "right" }}
                    disabled={rowData.product?.gsflag === 19}
                    min={0}
                    mode="decimal"
                    minFractionDigits={3}
                    maxFractionDigits={3}
                    useGrouping={false}
                />
            )}
        />
    );

    const amountBodyTemplate = (
        rowData: InvoiceItem,
        { rowIndex }: { rowIndex: number }
    ) => (
        <Controller
            name={`invoiceItems.${rowIndex}.amount`}
            control={control}
            rules={{
                required: "Please enter the amount.",
                min: {
                    value: 0.001,
                    message: "Amount must be greater than 0.",
                },
            }}
            render={({ field, fieldState }) => (
                <InputNumber
                    value={field.value}
                    onValueChange={(e) => {
                        field.onChange(e.value);
                        updateInvoiceItem(rowIndex, "amount", e.value || 0.0);
                    }}
                    placeholder="0.00"
                    className={`w-full ${
                        fieldState.invalid ? "p-invalid" : ""
                    }`}
                    inputStyle={{ textAlign: "right" }}
                    min={0}
                    mode="decimal"
                    minFractionDigits={2}
                    maxFractionDigits={2}
                    useGrouping={false}
                />
            )}
        />
    );

    const discountBodyTemplate = (
        rowData: InvoiceItem,
        { rowIndex }: { rowIndex: number }
    ) => (
        <div className="flex items-center gap-2">
            <Controller
                name={`invoiceItems.${rowIndex}.discount`}
                control={control}
                render={({ field }) => (
                    <InputNumber
                        value={field.value}
                        onValueChange={(e) => {
                            const value = e.value || 0.0;
                            if (discountType === "percentage" && value > 100) {
                                toast.current?.show({
                                    severity: "warn",
                                    summary: "Invalid Discount",
                                    detail: "Percentage discount cannot exceed 100%",
                                    life: 3000,
                                });
                                return;
                            }
                            field.onChange(e.value);
                            updateInvoiceItem(
                                rowIndex,
                                "discount",
                                e.value ||
                                    (discountType === "percentage" ? 0.0 : 0.0)
                            );
                        }}
                        placeholder={
                            discountType === "amount" ? "0.00" : "0.00"
                        }
                        className="w-20"
                        min={0}
                        mode="decimal"
                        minFractionDigits={2}
                        maxFractionDigits={2}
                        suffix={discountType === "percentage" ? "%" : undefined}
                        inputStyle={{ textAlign: "right" }}
                        useGrouping={false}
                    />
                )}
            />
            <span className="text-sm text-gray-500">
                {discountType === "amount" ? "Amt" : "%"}
            </span>
        </div>
    );

    const discountHeader = (
        <div className="flex items-center gap-2">
            <span>Discount</span>
            <Dropdown
                value={discountType}
                options={[
                    { label: "Amt", value: "amount" },
                    { label: "%", value: "percentage" },
                ]}
                onChange={(e) => setDiscountType(e.value)}
                placeholder="Select Discount Type"
                className="p-dropdown-sm w-24 smaller-dropdown" // Added custom class
            />
        </div>
    );

    const taxableValueBodyTemplate = (rowData: InvoiceItem) => (
        <InputText
            value={rowData.taxableValue.toFixed(2)}
            disabled
            className="w-full"
            style={{ textAlign: "right" }}
            placeholder="0.00"
        />
    );

    //   const gstRateBodyTemplate = (rowData: InvoiceItem) => (
    //   <InputText
    //     value={(!orgGstin && invoiceType === 'sales') ? '0.00%' : (rowData.gstRate ? `${rowData.gstRate.toFixed(2)}%` : '0.00%')}
    //     disabled
    //     className="w-full"
    //     style={{ textAlign: 'right' }}
    //   />
    // );

    const gstRateBodyTemplate = (rowData: InvoiceItem) => (
        <InputText
            value={rowData.gstRate ? `${rowData.gstRate.toFixed(2)}%` : "0.00%"}
            disabled
            className="w-full"
            style={{ textAlign: "right" }}
        />
    );

    const gstAmountBodyTemplate = (rowData: InvoiceItem) => (
        <InputText
            value={rowData.gstAmount.toFixed(2)}
            disabled
            className="w-full"
            style={{ textAlign: "right" }}
            placeholder="0.00"
        />
    );

    const totalBodyTemplate = (rowData: InvoiceItem) => (
        <InputText
            value={rowData.total.toFixed(2)}
            disabled
            className="w-full"
            style={{ textAlign: "right" }}
            placeholder="0.00"
        />
    );

    const actionBodyTemplate = (
        rowData: InvoiceItem,
        { rowIndex }: { rowIndex: number }
    ) => {
        if (rowIndex === 0) return null;
        return (
            <Button
                type="button"
                icon="pi pi-trash"
                className="p-button-danger p-button-sm"
                onClick={() => deleteRow(rowIndex)}
            />
        );
    };

    // MobileInvoiceItem component
    const MobileInvoiceItem: React.FC<{ item: InvoiceItem; index: number }> = ({
        item,
        index,
    }) => {
        const {
            formState: { errors },
        } = useFormContext<
            InvoiceFormValues & { invoiceItems: InvoiceItem[] }
        >();
        const itemErrors =
            errors.invoiceItems && Array.isArray(errors.invoiceItems)
                ? errors.invoiceItems[index]
                : undefined;
        return (
            <Card key={item.id} className="mb-4">
                <div className="grid grid-cols-1 gap-4">
                    <div>
                        <label className="block text-sm font-medium mb-1">
                            Product/Service <span className="text-red-500">*</span>  
                        </label>                       

                        {productBodyTemplate(item, { rowIndex: index })}
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1">
                            HSN
                        </label>
                        {hsnBodyTemplate(item)}
                    </div>
                    {item.product?.gsflag !== 19 && (
                        <>
                            <div>
                                <label className="block text-sm font-medium mb-1">
                                    Qty <span className="text-red-500">*</span>
                                </label>  

                                {qtyBodyTemplate(item, { rowIndex: index })}
                                {itemErrors?.qty && (
                                    <small className="p-error">
                                        {itemErrors.qty.message}
                                    </small>
                                )}
                            </div>
                            <div>
                                <label className="block text-sm font-medium mb-1">
                                    Free Qty
                                </label>
                                {freeQtyBodyTemplate(item, { rowIndex: index })}
                            </div>
                        </>
                    )}
                    <div>
                        <label className="block text-sm font-medium mb-1">
                            Amount <span className="text-red-500">*</span>
                        </label>
                        {amountBodyTemplate(item, { rowIndex: index })}
                        {itemErrors?.amount && (
                            <small className="p-error">
                                {itemErrors.amount.message}
                            </small>
                        )}
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1">
                            Discount
                        </label>
                        {discountBodyTemplate(item, { rowIndex: index })}
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1">
                            Taxable Value <span className="text-red-500">*</span>
                        </label>
                        {taxableValueBodyTemplate(item)}
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1">
                            GST Rate
                        </label>
                        {gstRateBodyTemplate(item)}
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1">
                            GST Amount
                        </label>
                        {gstAmountBodyTemplate(item)}
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1">
                            Total <span className="text-red-500">*</span>
                        </label>
                        {totalBodyTemplate(item)}
                    </div>
                    <div className="flex justify-end">
                        {actionBodyTemplate(item, { rowIndex: index })}
                    </div>
                </div>
            </Card>
        );
    };

    // Form Submission
    const handleReset = () => {
        reset();
        setSelectedParty(null);
        setDiscountType("amount");
    };

    const onSubmit = async (formData: InvoiceFormValues) => {
        if (
            !selectedParty ||
            invoiceItemsFields.every((item) => !item.product)
        ) {
            toast.current?.show({
                severity: "warn",
                summary: "Validation",
                detail: "Please select a party and add at least one product",
                life: 3000,
            });
            return;
        }

        const selectedMethod = formData.paymentMethod;
        const paymentmode = paymentModeMap[selectedMethod];

        const payload: InvoicePayload = {
            icflag: 9,
            inoutflag: invoiceType === "sales" ? 15 : 9,
            invoiceno: formData.invoiceNo,
            taxstate: taxstate,
            sourcestate: sourcestate,
            orgstategstin: orgGstin,
            contents: invoiceItemsFields.map((item) => ({
                productcode: item.product?.productcode || 0,
                productname: item.product?.productdesc || "",
                quantity: item.qty,
                freeQuantity: item.freeQty,
                gstflag: taxstate === sourcestate ? 3 : 9,
                gstrate:
                    !orgGstin && invoiceType === "sales" ? 0.0 : item.gstRate,
                pricePerUnit: item.amount,
                gstamount:
                    !orgGstin && invoiceType === "sales" ? 0.0 : item.gstAmount,
                taxableAmount: item.taxableValue,
                productAmount: item.total,
                gsflag: item.product?.gsflag || 7,
                discountAmount:
                    discountType === "percentage"
                        ? (item.amount * item.qty * item.discount) / 100
                        : item.discount,
            })),
            roundoffflag: 0,
            invnarration: formData.narration,
            reversecharge: formData.reverseCharge === "No" ? 0 : 1,
            nettotal: totals.taxableValue,
            invoicetotal: totals.total,
            invoicetotalword: numberToWords(totals.total),
            amountpaid: paymentmode === 15 ? 0.0 : totals.total,
            invoicedate: formData.invoiceDate.toISOString().split("T")[0],
            partyname: selectedParty.custname,
            custid: selectedParty.custid,
            orgcode,
            ...(!sameAsBilling && {
                consignee: {
                    name: consignee.name,
                    address: consignee.address,
                    state: consignee.state,
                    pincode: consignee.pincode,
                    contact: consignee.contact,
                    email: consignee.email,
                    gstin: consignee.gstin,
                },
            }),
            paymentmode,
            transportationmode:
                formData.transportMode as TransportationModeType,
            ...(formData.transportMode === "By Road" &&
                formData.vehicleNo && { vehicleNo: formData.vehicleNo }),
            discflag: discountType === "amount" ? 1 : 16,
            ...(formData.dateofsupply &&
                formData.dateofsupply !== "" && {
                    dateofsupply: new Date(formData.dateofsupply)
                        .toISOString()
                        .split("T")[0],
                }),
        };

        const result = invoicePayloadSchema.safeParse(payload);
        if (!result.success) {
            toast.current?.show({
                severity: "error",
                summary: "Validation Error",
                detail: result.error.errors[0].message,
                life: 3300,
            });
            return;
        }

    try {
      
const response = await apiCall<InvoiceResponse>("POST", `${OLK_PATH}/invoice`, payload);
      if (
        response.data?.success === 1 ||
        response.data?.message?.includes('invalid input syntax for type integer')
      ) {
        toast.current?.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Invoice added successfully.',
        });
      }
      if (response.data?.invid) {
        router.push(`/dashboard/invoice/sales-and-purchase/view/${response.data.invid}`);
      }
      handleReset();
    } catch (err) {
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to create invoice',
        life: 3000,
      });
    }
  };

    const handleViewInvoices = () => {
        router.push(
            `/dashboard/invoice/sales-and-purchase/list?title=${encodeURIComponent(
                title
            )}`
        );
    };

    return (
        <OlkProvider>
            <Toast ref={toast} />
            <Card className="p-4">
                <div className="flex justify-content-between align-items-center mb-4">
                    <h2 className="text-xl md:text-2xl font-semibold">
                        {title}
                    </h2>
                    <div className="flex gap-2">
                        <Button
                            label="View"
                            size="small"
                            className="custom-button custom-button-raised outlined"
                            onClick={handleViewInvoices}
                        />
                        <Button
                            label="Edit"
                            className="custom-button custom-button-raised hideOnMobile"
                        />
                    </div>
                </div>
                <Divider />

                <FormProvider {...methods}>
                    <form
                        onSubmit={handleSubmit(onSubmit)}
                        className="p-fluid grid gap-4"
                    >
                        {/* Invoice No and Date */}
                        <Card className="w-full">
                            <div className="formgrid grid md:col-6">
                                <div className="field col-12 md:col-6">
                                                <label htmlFor="invoiceNo">Invoice No : </label>
                                                <span className="text-red-500"> *</span>
                                                <Controller
                                                    name="invoiceNo"
                                                    control={control}
                                                    rules={{
                                                        required: "Please enter the invoice no.",
                                                        pattern: {
                                                            value: /^(?![0\-\/])[A-Za-z0-9\-\/]{1,16}$/,
                                                            message: "Invoice number must be 1–16 characters. Cannot start with 0, - or /. Only A-Z, 0-9, -, / allowed.",
                                                        },
                                                    }}
                                                    render={({ field, fieldState }) => (
                                                        <div>
                                                            <InputText
                                                                id="invoiceNo"
                                                                {...field}
                                                                placeholder={invoiceNoLoading ? "Loading..." : "Please enter invoice No"}
                                                                className={`w-full ${fieldState.invalid ? "p-invalid" : ""}`}
                                                                disabled={invoiceNoLoading}
                                                                onChange={(e) => {
                                                                    const input = e.target.value;
                                                                    const sanitized = input.replace(/[^A-Za-z0-9\-\/]/g, "");
                                                                    const startsInvalid = /^[0\-\/]/.test(sanitized);
                                                                    const limited = sanitized.slice(0, 16);
                                                                    if (!startsInvalid) {
                                                                        field.onChange(limited);
                                                                    }
                                                                }}
                                                                onPaste={(e) => {
                                                                    e.preventDefault();
                                                                    const pasted = e.clipboardData.getData("text");
                                                                    const sanitized = pasted.replace(/[^A-Za-z0-9\-\/]/g, "").slice(0, 16);
                                                                    if (!/^[0\-\/]/.test(sanitized)) {
                                                                        field.onChange(sanitized);
                                                                    }
                                                                }}
                                                            />
                                                            {fieldState.error && (
                                                                <small className="p-error">
                                                                    {fieldState.error.message}
                                                                </small>
                                                            )}
                                                        </div>
                                                    )}
                                                />
                                            </div>

                                <div className="field col-12 md:col-6">
                                    <label htmlFor="invoiceDate">
                                        Invoice Date :{" "}
                                    </label>{" "}
                                    <span className="text-red-500">*</span>
                                    <Controller
                                        name="invoiceDate"
                                        control={control}
                                        render={({ field }) => (
                                            <Calendar
                                                id="invoiceDate"
                                                value={field.value}
                                                onChange={(e) =>
                                                    field.onChange(e.value)
                                                }
                                                dateFormat="dd/mm/yy"
                                                showIcon
                                                className="w-full"
                                                minDate={new Date(yearstart)}
                                                maxDate={new Date()}
                                            />
                                        )}
                                    />
                                </div>
                            </div>
                        </Card>

                        {/* Party Selector */}
                        <Card className="w-full">
                            <div className="grid align-items-center mb-3">
                                <div className="flex flex-column md:flex-row justify-between align-items-start md:align-items-center gap-4 w-full">
                                    <div className="flex-shrink-0  order-1 md:order-0 ">
                                        <h3 className="m-0 text-lg font-semibold whitespace-nowrap">
                                            {invoiceType === "purchase"
                                                ? "Details of Supplier"
                                                : "Details of Receiver | Billed to"}{" "}
                                            :{" "}
                                            <span className="text-red-500">
                                                *
                                            </span>{" "}
                                        </h3>
                                    </div>
                                    <div className="flex flex-column sm:flex-row gap-2 sm:align-items-center w-full md:w-auto">
                                        <label
                                            htmlFor="party"
                                            className="font-medium whitespace-nowrap"
                                        >
                                            Select Party :
                                        </label>
                                        <div className="flex-grow-1">
                                            {partyLoading ? (
                                                <ProgressSpinner
                                                    style={{
                                                        width: "1.2rem",
                                                        height: "1.5rem",
                                                    }}
                                                    strokeWidth="4"
                                                />
                                            ) : parties.length === 0 ? (
                                                <span>
                                                    No parties available
                                                </span>
                                            ) : (
                                                <Dropdown
                                                    id="party"
                                                    value={
                                                        selectedParty
                                                            ? selectedParty.custid
                                                            : null
                                                    }
                                                    onChange={(e) => {
                                                        const selectedParty =
                                                            parties.find(
                                                                (p) =>
                                                                    p.custid ===
                                                                    e.value
                                                            ) || null;
                                                        handlePartySelect(
                                                            selectedParty
                                                        );
                                                    }}
                                                    options={parties}
                                                    optionLabel="custname"
                                                    optionValue="custid"
                                                    placeholder="Select a party"
                                                    filter
                                                    className="w-full md:w-20rem surface-overlay border-round-md"
                                                    panelClassName="custom-dropdown-panel"
                                                    itemTemplate={(option) => (
                                                        <div
                                                            title={
                                                                option.custname
                                                            }
                                                            className="text-truncate"
                                                        >
                                                            {option.custname}
                                                        </div>
                                                    )}
                                                />
                                            )}
                                        </div>
                                    </div>
                                    <div className="field mt-3 flex px-2">
                                        <label className="block text-md font-medium mb-2">
                                            Reverse Charge :
                                        </label>
                                        <div className="flex gap-4 px-2">
                                            <div className="flex align-items-center">
                                                <Controller
                                                    name="reverseCharge"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <RadioButton
                                                            inputId="reverseChargeYes"
                                                            value="Yes"
                                                            checked={
                                                                field.value ===
                                                                "Yes"
                                                            }
                                                            onChange={(e) =>
                                                                field.onChange(
                                                                    e.value
                                                                )
                                                            }
                                                            disabled={
                                                                (orgGstin ===
                                                                    null &&
                                                                    selectedParty?.gstin ===
                                                                        "") || // Condition for both sales and purchase
                                                                (invoiceType ===
                                                                    "sales" &&
                                                                    orgGstin !==
                                                                        null) // Condition for sales when orgGstin exists
                                                            }
                                                        />
                                                    )}
                                                />
                                                <label
                                                    htmlFor="reverseChargeYes"
                                                    className="ml-2"
                                                >
                                                    Yes
                                                </label>
                                            </div>
                                            <div className="flex align-items-center">
                                                <Controller
                                                    name="reverseCharge"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <RadioButton
                                                            inputId="reverseChargeNo"
                                                            value="No"
                                                            checked={
                                                                field.value ===
                                                                "No"
                                                            }
                                                            onChange={(e) =>
                                                                field.onChange(
                                                                    e.value
                                                                )
                                                            }
                                                        />
                                                    )}
                                                />
                                                <label
                                                    htmlFor="reverseChargeNo"
                                                    className="ml-2"
                                                >
                                                    No
                                                </label>
                                            </div>
                                        </div>
                                    </div>{" "}
                                    <div className="flex self-end order-0 md:order-2 md:ml-auto">
                                        <Button
                                            type="button"
                                            label="Add Party"
                                            size="small"
                                            className="w-auto outlined custom-button custom-button-raised sm:flex-order-2 "
                                            style={{
                                                padding: "0.65rem 0.5rem",
                                                fontSize: "0.75rem",
                                            }}
                                            onClick={() =>
                                                setShowAddPartyDialog(true)
                                            } // Open the dialog
                                        />
                                    </div>
                                </div>
                                {/* Add Party Dialog */}
                                <Dialog
                                    visible={showAddPartyDialog}
                                    onHide={() => setShowAddPartyDialog(false)}
                                    header="Add Party"
                                    className="text-primary"
                                    modal
                                    style={{
                                        width: "90vw",
                                        maxHeight: "80vh", // Limit height to 80% of viewport height
                                        overflowY: "auto", // Enable vertical scrolling for overflow
                                    }}
                                    breakpoints={{
                                        "960px": "75vw",
                                        "641px": "90vw",
                                        "400px": "100vw", // Full width on very small screens
                                    }}
                                    contentStyle={{
                                        padding: "1rem", // Default padding
                                    }}
                                >
                                    <AddPartyForm
                                        visible={showAddPartyDialog}
                                        onHide={() =>
                                            setShowAddPartyDialog(false)
                                        }
                                        onSuccess={handlePartyAdded}
                                    />
                                </Dialog>
                                {selectedParty && (
                                    <>
                                        <Divider className="my-2" />
                                        <div className="grid md:col-6 p">
                                            <div className="col-12 md:col-6 py-2">
                                                <p className="m-0">
                                                    <label htmlFor="gstin">
                                                        GSTIN :{" "}
                                                    </label>
                                                    {selectedParty.gstin ||
                                                        "N/A"}
                                                </p>
                                                <p className="mt-2">
                                                    <label htmlFor="address">
                                                        Address :{" "}
                                                    </label>
                                                    {selectedParty.custaddr}
                                                </p>
                                            </div>
                                            <div className="col-12 md:col-6 py-2">
                                                <p className="m-0">
                                                    <label htmlFor="state">
                                                        State :{" "}
                                                    </label>
                                                    {selectedParty.state}
                                                </p>
                                                <p className="mt-2">
                                                    <label htmlFor="pincode">
                                                        Pincode :{" "}
                                                    </label>
                                                    {selectedParty.pincode}
                                                </p>
                                            </div>
                                        </div>

                                        <div className="flex align-items-center gap-4 mt-3">
                                            <Checkbox
                                                inputId="sameAsBilling"
                                                checked={sameAsBilling}
                                                onChange={(e) =>
                                                    setSameAsBilling(
                                                        e.checked ?? true
                                                    )
                                                }
                                            />
                                            <label
                                                htmlFor="sameAsBilling"
                                                className="font-medium"
                                            >
                                                Shipping address is same as
                                                billing address
                                            </label>
                                        </div>

                                        {!sameAsBilling && (
                                            <div className="formgrid grid mt-3 px-2">
                                                <div className="formgrid grid">
                                                    {/* Name */}
                                                    <div className="col-12 md:col-4 mb-3">
                                                        <label
                                                            htmlFor="consigneeName"
                                                            className="font-medium mb-2 block"
                                                        >
                                                            Name
                                                        </label>
                                                        <InputText
                                                            id="consigneeName"
                                                            value={
                                                                consignee.name
                                                            }
                                                            onChange={(e) =>
                                                                setConsignee({
                                                                    ...consignee,
                                                                    name: e
                                                                        .target
                                                                        .value,
                                                                })
                                                            }
                                                            className="w-full"
                                                        />
                                                    </div>

                                                    {/* Empty div to balance the grid on medium and larger screens */}
                                                    <div className="hidden md:block md:col-6 mb-3"></div>

                                                    {/* Address, State, Pincode Row */}
                                                    <div className="col-12 md:col-4 mb-3">
                                                        <label
                                                            htmlFor="consigneeAddress"
                                                            className="font-medium mb-2 block"
                                                        >
                                                            Address
                                                        </label>
                                                        <InputText
                                                            id="consigneeAddress"
                                                            value={
                                                                consignee.address
                                                            }
                                                            onChange={(e) =>
                                                                setConsignee({
                                                                    ...consignee,
                                                                    address:
                                                                        e.target
                                                                            .value,
                                                                })
                                                            }
                                                            className="w-full"
                                                        />
                                                    </div>

                                                    <div className="col-12 md:col-4 mb-3">
                                                        <label
                                                            htmlFor="consigneeState"
                                                            className="font-medium mb-2 block"
                                                        >
                                                            State
                                                        </label>
                                                        <Dropdown
                                                            id="consigneeState"
                                                            value={
                                                                consignee.state
                                                            }
                                                            filter
                                                            options={states}
                                                            onChange={(e) =>
                                                                setConsignee({
                                                                    ...consignee,
                                                                    state: e.value,
                                                                })
                                                            }
                                                            placeholder="Select State"
                                                            className="w-full"
                                                        />
                                                    </div>

                                                    <div className="col-12 md:col-4 mb-3">
                                                        <label
                                                            htmlFor="consigneePincode"
                                                            className="font-medium mb-2 block"
                                                        >
                                                            Pincode
                                                        </label>
                                                        <InputText
                                                            id="consigneePincode"
                                                            value={
                                                                consignee.pincode
                                                            }
                                                            onChange={(e) =>
                                                                setConsignee({
                                                                    ...consignee,
                                                                    pincode:
                                                                        e.target
                                                                            .value,
                                                                })
                                                            }
                                                            className="w-full"
                                                        />
                                                    </div>

                                                    {/* Email, Contact, GSTIN Row */}
                                                    <div className="col-12 md:col-4 mb-3">
                                                        <label
                                                            htmlFor="consigneeEmail"
                                                            className="font-medium mb-2 block"
                                                        >
                                                            Email
                                                        </label>
                                                        <Controller
                                                            name="consignee.email"
                                                            control={control}
                                                            rules={{
                                                                validate: (
                                                                    value
                                                                ) => {
                                                                    if (!value)
                                                                        return true;
                                                                    if (
                                                                        value.length <
                                                                        6
                                                                    )
                                                                        return "Email must be at least 6 characters";
                                                                    if (
                                                                        value.length >
                                                                        100
                                                                    )
                                                                        return "Email cannot exceed 100 characters";
                                                                    const pattern =
                                                                        /^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+$/;
                                                                    if (
                                                                        !pattern.test(
                                                                            value
                                                                        )
                                                                    )
                                                                        return "Please enter valid email.";
                                                                    return true;
                                                                },
                                                            }}
                                                            render={({
                                                                field,
                                                                fieldState,
                                                            }) => (
                                                                <div>
                                                                    <InputText
                                                                        id="consigneeEmail"
                                                                        {...field}
                                                                        className={`w-full ${
                                                                            fieldState.invalid
                                                                                ? "p-invalid"
                                                                                : ""
                                                                        }`}
                                                                        onChange={(
                                                                            e
                                                                        ) => {
                                                                            field.onChange(
                                                                                e
                                                                                    .target
                                                                                    .value
                                                                            );
                                                                            setValue(
                                                                                "consignee.email",
                                                                                e
                                                                                    .target
                                                                                    .value
                                                                            );
                                                                        }}
                                                                    />
                                                                    {fieldState.error && (
                                                                        <small className="p-error block mt-1">
                                                                            {
                                                                                fieldState
                                                                                    .error
                                                                                    .message
                                                                            }
                                                                        </small>
                                                                    )}
                                                                </div>
                                                            )}
                                                        />
                                                    </div>

                                                    <div className="col-12 md:col-4 mb-3">
                                                        <label
                                                            htmlFor="consigneeContact"
                                                            className="font-medium mb-2 block"
                                                        >
                                                            Contact
                                                        </label>
                                                        <Controller
                                                            name="consignee.contact"
                                                            control={control}
                                                            rules={{
                                                                validate: (
                                                                    value
                                                                ) => {
                                                                    if (!value)
                                                                        return true;
                                                                    if (
                                                                        !/^\d+$/.test(
                                                                            value
                                                                        )
                                                                    )
                                                                        return "Please enter valid contact.";
                                                                    if (
                                                                        value.length <
                                                                        6
                                                                    )
                                                                        return "Contact must be at least 6 digits";
                                                                    if (
                                                                        value.length >
                                                                        12
                                                                    )
                                                                        return "Contact cannot exceed 12 digits";
                                                                    return true;
                                                                },
                                                            }}
                                                            render={({
                                                                field,
                                                                fieldState,
                                                            }) => (
                                                                <div>
                                                                    <InputText
                                                                        id="consigneeContact"
                                                                        {...field}
                                                                        className={`w-full ${
                                                                            fieldState.invalid
                                                                                ? "p-invalid"
                                                                                : ""
                                                                        }`}
                                                                        onChange={(
                                                                            e
                                                                        ) => {
                                                                            field.onChange(
                                                                                e
                                                                                    .target
                                                                                    .value
                                                                            );
                                                                            setValue(
                                                                                "consignee.contact",
                                                                                e
                                                                                    .target
                                                                                    .value
                                                                            );
                                                                        }}
                                                                    />
                                                                    {fieldState.error && (
                                                                        <small className="p-error block mt-1">
                                                                            {
                                                                                fieldState
                                                                                    .error
                                                                                    .message
                                                                            }
                                                                        </small>
                                                                    )}
                                                                </div>
                                                            )}
                                                        />
                                                    </div>

                                                    <div className="col-12 md:col-4 mb-3">
                                                        <label
                                                            htmlFor="consigneeGstin"
                                                            className="font-medium mb-2 block"
                                                        >
                                                            GSTIN
                                                        </label>
                                                        <Controller
                                                            name="consignee.gstin"
                                                            control={control}
                                                            rules={{
                                                                validate: (
                                                                    value
                                                                ) => {
                                                                    if (!value)
                                                                        return true;
                                                                    const pattern =
                                                                        /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
                                                                    if (
                                                                        !pattern.test(
                                                                            value
                                                                        )
                                                                    )
                                                                        return "Invalid GSTIN format";
                                                                    return true;
                                                                },
                                                            }}
                                                            render={({
                                                                field,
                                                                fieldState,
                                                            }) => (
                                                                <div>
                                                                    <InputText
                                                                        id="consigneeGstin"
                                                                        {...field}
                                                                        className={`w-full ${
                                                                            fieldState.invalid
                                                                                ? "p-invalid"
                                                                                : ""
                                                                        }`}
                                                                        onChange={(
                                                                            e
                                                                        ) => {
                                                                            const upperCaseValue =
                                                                                e.target.value.toUpperCase();
                                                                            field.onChange(
                                                                                upperCaseValue
                                                                            );
                                                                            setValue(
                                                                                "consignee.gstin",
                                                                                upperCaseValue
                                                                            );
                                                                        }}
                                                                    />
                                                                    {fieldState.error && (
                                                                        <small className="p-error block mt-1">
                                                                            {
                                                                                fieldState
                                                                                    .error
                                                                                    .message
                                                                            }
                                                                        </small>
                                                                    )}
                                                                </div>
                                                            )}
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </>
                                )}
                            </div>
                        </Card>

                        {/* Product Table */}
                        <Card className="w-full">
                            {productLoading ? (
                                <div className="flex justify-center items-center h-48">
                                    <ProgressSpinner />
                                </div>
                            ) : (
                                <div className="flex flex-col gap-4 max-w-full overflow-x-auto">
                                    <div className="block md:hidden">
                                        <div className="flex items-center gap-2 mb-4">
                                            <label className="font-medium">
                                                Discount Type:
                                            </label>
                                            <Dropdown
                                                value={discountType}
                                                options={[
                                                    {
                                                        label: "Amt",
                                                        value: "amount",
                                                    },
                                                    {
                                                        label: "%",
                                                        value: "percentage",
                                                    },
                                                ]}
                                                onChange={(e) =>
                                                    setDiscountType(e.value)
                                                }
                                                placeholder="Select Discount Type"
                                                className="p-dropdown-sm w-24 smaller-dropdown" // Added custom class
                                            />
                                        </div>
                                        {invoiceItemsFields.map(
                                            (item, index) => (
                                                <MobileInvoiceItem
                                                    key={item.id}
                                                    item={item}
                                                    index={index}
                                                />
                                            )
                                        )}
                                        <div className="flex flex-col gap-4 mt-4 invoice-footer">
                                            <div className="flex justify-end gap-2">
                                                <Button
                                                    type="button"
                                                    label="Add Product"
                                                    icon="pi pi-plus"
                                                    size="small"
                                                    style={{
                                                        padding:
                                                            "0.50rem 0.5rem",
                                                        fontSize: "0.75rem",
                                                    }}
                                                    className="p-button-sm w-auto"
                                                    onClick={addNewRow}
                                                    disabled={
                                                        invoiceItemsFields.length >=
                                                        products.length
                                                    }
                                                />
                                            </div>
                                            <div className="flex justify-end flex-wrap gap-4 text-center font-bold">
                                                <div>
                                                    Taxable Value: ₹
                                                    {totals.taxableValue.toFixed(
                                                        2
                                                    )}
                                                </div>
                                                <div>
                                                    GST Amount: ₹
                                                    {totals.gstAmount.toFixed(
                                                        2
                                                    )}
                                                </div>
                                                <div>
                                                    Total: ₹
                                                    {totals.total.toFixed(2)}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="hidden md:block">
                                        <DataTable
                                            value={invoiceItemsFields}
                                            className="w-full max-w-full"
                                            scrollable
                                            scrollHeight="auto"
                                            footer={
                                                <div className="invoice-footer text-center font-bold">
                                                    <div className="button-container flex gap-2">
                                                        <Button
                                                            label="Add Product"
                                                            icon="pi pi-plus"
                                                            size="small"
                                                            className="olk-button p-button-sm w-auto"
                                                            style={{
                                                                padding:
                                                                    "0.50rem 0.5rem",
                                                                fontSize:
                                                                    "0.75rem",
                                                            }}
                                                            onClick={addNewRow}
                                                            disabled={
                                                                invoiceItemsFields.length >=
                                                                products.length
                                                            }
                                                        />
                                                    </div>
                                                    <div className="flex justify-end flex-wrap gap-4">
                                                        <div>
                                                            Taxable Value: ₹
                                                            {totals.taxableValue.toFixed(
                                                                2
                                                            )}
                                                        </div>
                                                        <div>
                                                            GST Amount: ₹
                                                            {totals.gstAmount.toFixed(
                                                                2
                                                            )}
                                                        </div>
                                                        <div>
                                                            Total: ₹
                                                            {totals.total.toFixed(
                                                                2
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        >
                                            <Column
                                                header="Product/Service *" 
                                                body={productBodyTemplate}
                                                style={{ minWidth: "200px" }}
                                            />
                                            <Column
                                                header="HSN"
                                                body={hsnBodyTemplate}
                                                style={{ minWidth: "100px" }}
                                            />
                                            <Column
                                                header="Qty *"
                                                body={qtyBodyTemplate}
                                                style={{ minWidth: "100px" }}
                                            />
                                            <Column
                                                header="Free Qty"
                                                body={freeQtyBodyTemplate}
                                                style={{ minWidth: "100px" }}
                                            />
                                            <Column
                                                header="Amount *"
                                                body={amountBodyTemplate}
                                                style={{ minWidth: "120px" }}
                                            />
                                            <Column
                                                header={discountHeader}
                                                body={discountBodyTemplate}
                                                style={{ minWidth: "100px" }}
                                            />
                                            <Column
                                                header="Taxable Value *"
                                                body={taxableValueBodyTemplate}
                                                style={{ minWidth: "120px" }}
                                            />
                                            <Column
                                                header="GST"
                                                body={gstRateBodyTemplate}
                                                style={{ minWidth: "100px" }}
                                            />
                                            <Column
                                                header="GST Amount"
                                                body={gstAmountBodyTemplate}
                                                style={{ minWidth: "120px" }}
                                            />
                                            <Column
                                                header="Total *"
                                                body={totalBodyTemplate}
                                                style={{ minWidth: "120px" }}
                                            />
                                            <Column
                                                header="Action"
                                                body={actionBodyTemplate}
                                                style={{ minWidth: "80px" }}
                                            />
                                        </DataTable>
                                    </div>
                                </div>
                            )}
                        </Card>

                        {/* Additional Sections: Payment Details, Mode of Transportation, Invoice Summary */}
                        <div className="grid card-grid-container mt-4 md:p-2 gap-3">
                            {/* Payment Details */}
                            <Card className="col-12 md:col-4 my-3">
                                <div className="field">
                                    <label htmlFor="paymentMethod">
                                        Payment Method :{" "}
                                        <span className="text-red-500">*</span>{" "}
                                    </label>
                                    <Controller
                                        name="paymentMethod"
                                        control={control}
                                        rules={{
                                            required:
                                                "Please select a payment method.",
                                        }}
                                        render={({ field }) => (
                                            <Dropdown
                                                id="paymentMethod"
                                                {...field}
                                                options={paymentOptions}
                                                placeholder="Select Payment Method"
                                                className="w-full"
                                            />
                                        )}
                                    />
                                </div>

                                {paymentMethod === "Cash" && (
                                    <div className="field mt-3">
                                        <label style={{ color: "green" }}>
                                            Cash Received.
                                        </label>
                                    </div>
                                )}
                                {paymentMethod === "Bank" && (
                                    <div className="field mt-3">
                                        <label style={{ color: "green" }}>
                                            Received Bank Transfer.
                                        </label>
                                    </div>
                                )}
                                {paymentMethod === "Online" && (
                                    <div className="field mt-3">
                                        <label style={{ color: "green" }}>
                                            Online Payment Received.
                                        </label>
                                    </div>
                                )}
                                {paymentMethod === "On Credit" && (
                                    <div className="field mt-3">
                                        <label style={{ color: "red" }}>
                                            Payment Pending.
                                        </label>
                                    </div>
                                )}
                            </Card>

                            {/* Mode of Transportation */}
                            <Card className="col-12 md:col-4 my-3">
                                <h3 className="text-lg font-semibold mb-3">
                                    Mode of Transportation
                                </h3>
                                <div className="field">
                                    <Controller
                                        name="transportMode"
                                        control={control}
                                        render={({ field }) => (
                                            <Dropdown
                                                id="transportMode"
                                                {...field}
                                                options={transportOptions}
                                                placeholder="Select Transport Mode"
                                                className="w-full"
                                            />
                                        )}
                                    />
                                </div>
                                {watch("transportMode") === "By Road" && (
                                    <div className="field mt-3">
                                        <label htmlFor="vehicleNo">
                                            Vehicle No.
                                        </label>
                                        <Controller
                                            name="vehicleNo"
                                            control={control}
                                            render={({ field }) => (
                                                <InputText
                                                    id="vehicleNo"
                                                    {...field}
                                                    placeholder="Enter vehicle number"
                                                    className="w-full"
                                                />
                                            )}
                                        />
                                    </div>
                                )}
                                <div className="field mt-3">
                                    <label htmlFor="dateofsupply">
                                        Date of supply :
                                    </label>
                                    <Controller
                                        name="dateofsupply"
                                        control={control}
                                        render={({ field }) => {
                                            const isValidDate = (
                                                dateString: string
                                            ) => {
                                                const date = new Date(
                                                    dateString
                                                );
                                                return (
                                                    dateString &&
                                                    !isNaN(date.getTime())
                                                );
                                            };
                                            return (
                                                <Calendar
                                                    id="dateofsupply"
                                                    value={
                                                        isValidDate(field.value)
                                                            ? new Date(
                                                                  field.value
                                                              )
                                                            : null
                                                    }
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            e.value
                                                                ? e.value
                                                                      .toISOString()
                                                                      .split(
                                                                          "T"
                                                                      )[0]
                                                                : ""
                                                        )
                                                    }
                                                    dateFormat="yy-mm-dd"
                                                    showIcon
                                                    className="w-full"
                                                    placeholder="Select a date"
                                                />
                                            );
                                        }}
                                    />
                                </div>
                            </Card>

                            {/* Invoice Summary */}
                            <Card className="col-12 md:col-4 my-3">
                                <h3 className="text-lg font-semibold mb-3">
                                    Invoice Summary
                                </h3>
                                <div className="invoice-summary-container">
                                    <div className="invoice-summary-row">
                                        <span className="w-32 text-left">
                                            Total Taxable Amount :{" "}
                                        </span>
                                        <span className="flex-1 text-right">
                                            ₹{totals.taxableValue.toFixed(2)}
                                        </span>
                                    </div>
                                    {orgGstin || invoiceType !== "sales" ? (
                                        <div className="">
                                            {taxstate === sourcestate ? (
                                                <>
                                                    <div className="invoice-summary-row">
                                                        <span className="w-32 text-left">
                                                            Total CGST :{" "}
                                                        </span>
                                                        <span className="flex-1 text-right">
                                                            ₹
                                                            {(
                                                                totals.gstAmount /
                                                                2
                                                            ).toFixed(2)}
                                                        </span>
                                                    </div>
                                                    <br />
                                                    <div className="invoice-summary-row">
                                                        <span className="w-32 text-left">
                                                            Total SGST :{" "}
                                                        </span>
                                                        <span className="flex-1 text-right">
                                                            ₹
                                                            {(
                                                                totals.gstAmount /
                                                                2
                                                            ).toFixed(2)}
                                                        </span>
                                                    </div>
                                                </>
                                            ) : (
                                                <div className="invoice-summary-row">
                                                    <span className="w-32 text-left">
                                                        Total IGST :{" "}
                                                    </span>
                                                    <span className="flex-1 text-right">
                                                        ₹
                                                        {totals.gstAmount.toFixed(
                                                            2
                                                        )}
                                                    </span>
                                                </div>
                                            )}
                                        </div>
                                    ) : null}
                                    <div className="invoice-summary-row">
                                        <span className="w-32 text-left">
                                            Total Discount:
                                        </span>
                                        <span className="flex-1 text-right">
                                            ₹{totals.totalDiscount.toFixed(2)}
                                        </span>
                                    </div>
                                    <div className="invoice-summary-row font-bold">
                                        <span className="w-32 text-left">
                                            Total Value:
                                        </span>
                                        <span className="flex-1 text-right">
                                            ₹{totals.total.toFixed(2)}
                                        </span>
                                    </div>
                                </div>
                                <div className="field mt-3">
                                    <label className="block text-md font-medium mb-1">
                                        Invoice Total in Words :{" "}
                                    </label>
                                    <p
                                        className="text-gray-700"
                                        style={{ fontStyle: "italic" }}
                                    >
                                        {numberToWords(totals.total)} Only
                                    </p>
                                </div>
                            </Card>
                        </div>

                        {/* Narration */}
                        <Card className="col-12 md:col-8 form.footer">
                            <div className="field">
                                <label htmlFor="narration">Narration</label>
                                <Controller
                                    name="narration"
                                    control={control}
                                    render={({ field }) => (
                                        <InputText
                                            id="narration"
                                            {...field}
                                            placeholder="Add narration"
                                            className="w-full"
                                        />
                                    )}
                                />
                            </div>
                        </Card>

                        {/* Footer Buttons */}
                        <div className="col-4  gap-2 flex justify-start mt-4 pr-3">
                            <Button
                                label="Reset"
                                icon="pi pi-undo"
                                className="p-button-secondary"
                                type="button"
                                onClick={handleReset}
                            />
                            <Button
                                label="Save"
                                icon="pi pi-save"
                                className="p-button-success"
                                type="submit"
                            />
                        </div>
                    </form>
                </FormProvider>
            </Card>
        </OlkProvider>
    );
};

export default InvoiceForm;
