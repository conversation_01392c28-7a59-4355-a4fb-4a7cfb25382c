// import { Dropdown } from "primereact/dropdown";
// import { useTranslations } from "next-intl";

// export default function TransportMode() {
//   const t = useTranslations("TransportMode");

//   const transportOptions = [
//     t("modes.none"),
//     t("modes.road"),
//     t("modes.rail"),
//     t("modes.air"),
//     t("modes.ship")
//   ];

//   return (
//     <div className="my-4">
//       <label>{t("labels.modeOfTransport")}</label>
//       <Dropdown
//         options={transportOptions}
//         placeholder={t("placeholders.selectMode")}
//         className="w-full"
//       />
//     </div>
//   );
// }