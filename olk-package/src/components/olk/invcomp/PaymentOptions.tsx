// "use client";
// import { Dropdown } from "primereact/dropdown";
// import { InputText } from "primereact/inputtext";
// import { useState } from "react";
// import { useTranslations } from "next-intl";

// export default function PaymentOptions() {
//   const [method, setMethod] = useState<string>("");
//   const t = useTranslations("PaymentOptions");

//   const paymentMethods = [
//     t("methods.cash"),
//     t("methods.bank"),
//     t("methods.credit"),
//     t("methods.online")
//   ];

//   return (
//     <div className="col-12 md:col-6 my-4">
//       <label htmlFor="paymentMode" className="block mb-2 font-medium">
//         {t("labels.paymentMode")}
//       </label>
//       <Dropdown
//         id="paymentMode"
//         value={method}
//         options={paymentMethods}
//         onChange={(e) => setMethod(e.value)}
//         placeholder={t("placeholders.selectPaymentMode")}
//         className="w-full"
//       />

//       {method === t("methods.cash") && (
//         <div className="mt-3">
//           <label htmlFor="cashReceived" className="block mb-1 font-medium">
//             {t("labels.cashReceived")}
//           </label>
//           <InputText 
//             id="cashReceived" 
//             placeholder={t("placeholders.cashReceived")} 
//             className="w-full" 
//           />
//         </div>
//       )}

//       {method === t("methods.bank") && (
//         <div className="mt-3 formgrid grid">
//           <div className="col-12">
//             <label htmlFor="bankName" className="block mb-1 font-medium">
//               {t("labels.bankName")}
//             </label>
//             <InputText 
//               disabled 
//               id="bankName" 
//               placeholder={t("placeholders.bankName")} 
//               className="w-full" 
//             />
//           </div>
//           <div className="col-12 md:col-6 mt-3 md:mt-0">
//             <label htmlFor="accountNo" className="block mb-1 font-medium">
//               {t("labels.accountNo")}
//             </label>
//             <InputText 
//               disabled 
//               id="accountNo" 
//               placeholder={t("placeholders.accountNo")} 
//               className="w-full" 
//             />
//           </div>
//           <div className="col-12 md:col-6 mt-3 md:mt-0">
//             <label htmlFor="ifsc" className="block mb-1 font-medium">
//               {t("labels.ifsc")}
//             </label>
//             <InputText 
//               disabled 
//               id="ifsc" 
//               placeholder={t("placeholders.ifsc")} 
//               className="w-full" 
//             />
//           </div>
//         </div>
//       )}

//       {method === t("methods.online") && (
//         <div className="mt-3">
//           <label htmlFor="txnId" className="block mb-1 font-medium">
//             {t("labels.txnId")}
//           </label>
//           <InputText 
//             id="txnId" 
//             placeholder={t("placeholders.txnId")} 
//             className="w-full" 
//           />
//         </div>
//       )}
//     </div>
//   );
// }