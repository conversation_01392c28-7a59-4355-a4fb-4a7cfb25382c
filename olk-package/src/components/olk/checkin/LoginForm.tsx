"use client";

import { useState } from "react";
import { InputText } from "primereact/inputtext";
import { But<PERSON> } from "primereact/button";
import { useOrg } from "../../../contexts/orgContext";
import apiCall from '../../../utils/apiCallService';
import { useEnvContext } from '../../../contexts/EnvContextProvider';
import { getOrgDataField } from '../../../utils/cookies';
import Cookies from "js-cookie";
//import axiosInstance from "@/utils/axios";
import { useTranslations } from "next-intl";

export default function LoginForm() {
    const [email, setEmail] = useState("");
    const { setOrgData } = useOrg();
    const { OLK_PATH } = useEnvContext();
    const t = useTranslations("LoginForm");

    interface orgDetails {
        orgcode:number,
        orgname:string,
        yearstart:Date,
        yearend :Date,
        orgstate:string
    }


interface LoginResponse {
        olkstatus: number;
        orgcode:number,
        orgname:string,
        yearstart:Date,
        yearend :Date,
        orgstate:string}

    const handleLogin = async () => {
        const res = await apiCall<LoginResponse>("GET", `${OLK_PATH}/organisations/checkin?emflag=5&subemail=${email}`);
        const result = res.data;
        if (result.orgcode) {
            setOrgData({ orgcode: result.orgcode, orgname: result.orgname });
            Cookies.set(
                "orgdata",
                JSON.stringify({
                    orgcode: result.orgcode,
                    orgname: result.orgname,
                    yearstart: result.yearstart,
                    yearend: result.yearend,
                    orgstate: result.orgstate,
                }),
                {
                    secure: process.env.NODE_ENV === "production",
                    sameSite: "Strict",
                    expires: 1, // 1 day
                }
            );
        } else {
            alert(t("loginFailed"));
        }
    };

    return (
        <div className="card">
            <h3>{t("loginTitle")}</h3>
            <InputText
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder={t("emailPlaceholder")}
            />
            <Button
                label={t("loginButton")}
                onClick={handleLogin}
                className="mt-2"
            />
        </div>
    );
}
