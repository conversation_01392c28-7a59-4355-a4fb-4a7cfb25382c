'use client';

import { useRef } from 'react';
import { useForm, FieldErrors } from 'react-hook-form';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { useOrg } from "../../../contexts/orgContext";
import Cookies from 'js-cookie';
//import axiosInstance from "@/utils/axios";
import { useEnvContext } from '../../../contexts/EnvContextProvider';
import { useTranslations } from 'next-intl';
import apiCall from '../../../utils/apiCallService';


// Define interfaces for TypeScript
interface OrgDetails {
  orgname: string;
  orgtype: string;
  yearstart: string;
  yearend: string;
  orgcity: string;
  orgstate: string;
  orgaddr: string;
  orgpincode: string;
  orgpan?: string;
  gstin?: string;
}

interface UserDetails {
  subemail: string;
  subname?: string;
  submob?: string;
}

interface Payload {
  orgdetails: OrgDetails;
  userdetails: UserDetails;
}

// Define form data interface
interface FormData {
  orgname: string;
  orgpan?: string;
  gstin?: string;
  subemail: string;
  subname?: string;
  submob?: string;
}

interface regResponse{
  olkstatus:Number;
  yearstart:Date;
  yearend:Date;
  orgstate: string;
  neworgcode: number;
}

export default function RegistrationForm() {
  Cookies.remove('orgcode');
  const { setOrgData } = useOrg();
  const toast = useRef<Toast>(null);
  const t = useTranslations('RegistrationForm');

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({ mode: 'onChange' });
  const { OLK_PATH } = useEnvContext();

  const onSubmit = async (data: FormData) => {
    // Trim input values
    const trimmedData = {
      orgname: data.orgname?.trim(),
      orgpan: data.orgpan?.trim(),
      gstin: data.gstin?.trim(),
      subemail: data.subemail?.trim(),
      subname: data.subname?.trim(),
      submob: data.submob?.trim(),
    };

    // Build payload
    const payload: Payload = {
      orgdetails: {
        orgname: trimmedData.orgname,
        orgtype: "PVT LTD",
        yearstart: "2025-04-01",
        yearend: "2026-03-31",
        orgcity: "Mumbai",
        orgstate: "Maharashtra",
        orgaddr: "Tower 2 3rd Floor, Infotech Park, Vashi, Navi Mumbai",
        orgpincode: "400044",
      },
      userdetails: {
        subemail: trimmedData.subemail,
        subname: trimmedData.subname,
        submob: trimmedData.submob,
      },
    };

    // Conditionally add orgpan and gstin only if they are non-empty and valid
    if (trimmedData.orgpan && trimmedData.orgpan !== '') {
      payload.orgdetails.orgpan = trimmedData.orgpan;
    }
    if (trimmedData.gstin && trimmedData.gstin !== '') {
      payload.orgdetails.gstin = trimmedData.gstin;
    }

    console.log(payload);
    try {
      const res = await apiCall<regResponse>("POST", `${OLK_PATH}/organisations`,payload);
      const result = res.data;

      if (result?.olkstatus === 0) {
        setOrgData({
          orgcode: result.neworgcode,
          orgname: payload.orgdetails.orgname,
        });

        // Set orgdata in a cookie
        Cookies.set('orgdata', JSON.stringify({
          orgcode: result.neworgcode,
          orgname: payload.orgdetails.orgname,
          yearstart: payload.orgdetails.yearstart,
          yearend: payload.orgdetails.yearend,
          orgstate: payload.orgdetails.orgstate,
        }), {
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'Strict',
          expires: 1, // 1 day
        });

        console.log(Cookies.get('orgdata'));

        toast.current?.show({
          severity: "success",
          summary: t('successSummary'),
          detail: t('successDetail'),
        });
      } else {
        throw new Error("Invalid response");
      }
     } 
  catch (error) {
      console.log(error);
      toast.current?.show({
        severity: "error",
        summary: t('errorSummary'),
        detail: t('errorDetail'),
      });
    }
  
 };

  return (
    <div className="card">
      <Toast ref={toast} />
      <h3>{t('registerTitle')}</h3>
      <form onSubmit={handleSubmit(onSubmit)} className="grid gap-2">
        <InputText
          placeholder={t('orgNamePlaceholder')}
          {...register('orgname', {
            required: t('orgNameRequired'),
            validate: value => !!value.trim() || t('orgNameEmpty'),
          })}
        />
        {errors.orgname?.message && <small className="p-error">{errors.orgname.message}</small>}
        
        <InputText
          placeholder={t('panPlaceholder')}
          {...register('orgpan', {
            pattern: {
              value: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
              message: t('invalidPanFormat'),
            },
          })}
        />
        {errors.orgpan?.message && <small className="p-error">{errors.orgpan.message}</small>}
        
        <InputText
          placeholder={t('gstinPlaceholder')}
          {...register('gstin', {
            pattern: {
              value: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
              message: t('invalidGstinFormat'),
            },
          })}
        />
        {errors.gstin?.message && <small className="p-error">{errors.gstin.message}</small>}

        <InputText
          placeholder={t('emailPlaceholder')}
          {...register('subemail', {
            required: t('emailRequired'),
            validate: value => !!value.trim() || t('emailEmpty'),
            pattern: {
              value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
              message: t('invalidEmailFormat'),
            },
          })}
        />
        {errors.subemail?.message && <small className="p-error">{errors.subemail.message}</small>}

        <InputText 
          placeholder={t('namePlaceholder')} 
          {...register('subname')} 
        />
        <InputText 
          placeholder={t('mobilePlaceholder')} 
          {...register('submob')} 
        />

        <Button 
          type="submit" 
          label={t('registerButton')} 
          className="mt-2" 
        />
      </form>
    </div>
  );
}