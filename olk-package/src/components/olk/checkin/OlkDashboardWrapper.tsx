import RegistrationForm from './RegistrationForm'
import LoginForm from './LoginForm';
import OlkDashboard  from '../Dashboard/olkDashboard';
import { useOrg } from "../../../contexts/orgContext";


export default function OlkDashboardWrapper() {
  const { orgcode } = useOrg();

  if (!orgcode) {
    return (
      <>
        <RegistrationForm />
        <LoginForm />
      </>
    );
  }

  return <OlkDashboard/>;
}
