"use client";

import { useState } from "react";
import { InputText } from "primereact/inputtext";
import { Button } from "primereact/button";
import Cookies from "js-cookie";
import { useTranslations } from "next-intl";
import { useOrg } from "../contexts/orgContext";
import { useEnvContext } from "../contexts/EnvContextProvider";

export default function LoginForm() {
    const [email, setEmail] = useState("");
    const { setOrgData } = useOrg();
    const { OLK_PATH } = useEnvContext();
    const t = useTranslations("LoginForm");

    const handleLogin = async () => {
        console.log(
            `${OLK_PATH}/organisations/checkin?emflag=5&subemail=${email}`
        );
        //const res = await axiosInstance.get(`${OLK_PATH}/organisations/checkin?emflag=5&subemail=${email}`);

        const res = await fetch(
            `${OLK_PATH}/organisations/checkin?emflag=5&subemail=${email}`
        );
        const result = await res.json();
        if (result.orgcode) {
            setOrgData({ orgcode: result.orgcode, orgname: result.orgname });
            Cookies.set(
                "orgdata",
                JSON.stringify({
                    orgcode: result.orgcode,
                    orgname: result.orgname,
                    yearstart: result.yearstart,
                    yearend: result.yearend,
                    orgstate: result.orgstate,
                }),
                {
                    secure: process.env.NODE_ENV === "production",
                    sameSite: "Strict",
                    expires: 1, // 1 day
                }
            );
        } else {
            alert(t("loginFailed"));
        }
    };

    return (
        <div className="card">
            <h3>{t("loginTitle")}</h3>
            <InputText
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder={t("emailPlaceholder")}
            />
            <Button
                label={t("loginButton")}
                onClick={handleLogin}
                className="mt-2"
            />
        </div>
    );
}
