"use client";

import { NextIntlClientProvider } from "next-intl";
import React, { useEffect, useState } from "react";
import { getMessages } from "../utils/getMessages";
import Cookies from "js-cookie";
import { EnvProvider } from "./EnvContextProvider";
import { OrgProvider } from "./orgContext";

interface OlkProviderProps {
    children: React.ReactNode;
    locale?: string;
    env?: { OLK_PATH: string };
}

export function OlkProvider({ children, locale, env }: OlkProviderProps) {
    const [messages, setMessages] = useState<Record<string, any> | null>(null);
    const [resolvedLocale, setResolvedLocale] = useState(locale || "en-US");
    const environment = env || {
        OLK_PATH: "http://localhost:8082",
    };

    useEffect(() => {
        const cookieLocale = Cookies.get("NEXT_LOCALE");
        const finalLocale = locale || cookieLocale || "en-US";
        setResolvedLocale(finalLocale);

        async function loadMessages() {
            try {
                const loadedMessages = await getMessages(finalLocale);
                setMessages(loadedMessages);
            } catch (error) {
                console.error("Failed to load messages:", error);
                // Fallback to empty messages to prevent rendering issues
                setMessages({});
            }
        }

        loadMessages();
    }, [locale]);

    if (!messages) {
        return null;
    }

    return (
        <EnvProvider env={environment}>
            <OrgProvider>
                <NextIntlClientProvider
                    locale={resolvedLocale}
                    messages={messages}
                >
                    {children}
                </NextIntlClientProvider>
            </OrgProvider>
        </EnvProvider>
    );
}
