'use client';

import React, { createContext, useContext, useState } from 'react';

export type OrgContextType = {
  orgcode: number | null;
  orgname: string;
  setOrgData: (data: { orgcode: number; orgname: string }) => void;
};

const OrgContext = createContext<OrgContextType | undefined>(undefined);

export const OrgProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [orgcode, setOrgcode] = useState<number | null>(null);
  const [orgname, setOrgname] = useState<string>('');

  const setOrgData = ({ orgcode, orgname }: { orgcode: number; orgname: string }) => {
    setOrgcode(orgcode);
    setOrgname(orgname);
  };

  return (
    <OrgContext.Provider value={{ orgcode, orgname, setOrgData }}>
      {children}
    </OrgContext.Provider>
  );
};

export const useOrg = (): OrgContextType => {
  const context = useContext(OrgContext);
  if (!context) {
    throw new Error('useOrg must be used within an OrgProvider');
  }
  return context;
};


