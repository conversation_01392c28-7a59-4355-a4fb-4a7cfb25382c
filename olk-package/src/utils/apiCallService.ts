import axios, { AxiosRequestConfig, InternalAxiosRequestConfig } from "axios";
import { getCookie } from "cookies-next";

interface CustomAxiosConfig extends AxiosRequestConfig {
    allow404?: boolean;
    allow409?: boolean;
}

// Function to get the backend JWT from the cookies
const getBackendJwt = (): string | null => {
    return getCookie("backendJwt") as string | null;
};

// Function to get the locale from the cookies
const getLocale = (): string | null => {
    const localeFromCookie = getCookie("NEXT_LOCALE");
    return (localeFromCookie as string) || "en-US";
};

const apiClient = axios.create({
    headers: {
        "Content-Type": "application/json",
    },
});

apiClient.interceptors.request.use(
    (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
        const locale = getLocale();

        if (!config.headers["Authorization"]) {
            const backendJwt = getBackendJwt();
            if (backendJwt) {
                config.headers["Authorization"] = `Bearer eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************.JoP0H055pQmpCeEGVEy-PdMVYgXw6p7iL3wZWZNEHEE`;
            }
        }

        if (locale) {
            config.headers["Accept-Language"] = locale;
        }

        return config;
    },
    (error) => Promise.reject(error)
);

interface ApiResponse<T> {
    data: T;
    status: number;
    statusText: string;
    headers: any;
    config: AxiosRequestConfig;
}

const apiCall = async <T>(
    method: string,
    url: string,
    data?: any,
    config?: CustomAxiosConfig
): Promise<ApiResponse<T>> => {
    try {
        // Destructure our custom flags so they aren't passed to Axios
        const { allow404, allow409, ...axiosConfig } = config || {};

        // Build a custom validateStatus function based on provided flags.
        const customValidateStatus = (status: number) => {
            const baseValid = status >= 200 && status < 300;
            const valid422 = status === 422;
            const valid404 = allow404 ? status === 404 : false;
            const valid409 = allow409 ? status === 409 : false;
            return baseValid || valid422 || valid404 || valid409;
        };

        const response = await apiClient.request<T>({
            method,
            url,
            data,
            validateStatus: customValidateStatus,
            ...axiosConfig,
        });

        return {
            data: response.data,
            status: response.status,
            statusText: response.statusText,
            headers: response.headers,
            config: response.config,
        };
    } catch (error: unknown) {
        if (axios.isAxiosError(error)) {
            console.error("API Error:", error.response?.data || error.message);
            throw new Error(
                error.response?.data?.message || "Something went wrong"
            );
        } else {
            console.error("Unexpected error:", error);
            throw new Error("Something went wrong");
        }
    }
};

export default apiCall;
