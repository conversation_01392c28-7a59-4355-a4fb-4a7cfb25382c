import Cookies from 'js-cookie';

export async function getMessages(locale?: string) {
  const determinedLocale = locale || 'en-US';

  console.log('Determined locale:', determinedLocale);
  
  try {
    // Dynamic import based on locale
    const messages = await import(`../locales/${determinedLocale}.json`);
    return messages.default;
  } catch (error) {
    console.error(`Failed to load messages for locale: ${determinedLocale}`, error);
    
    // Fallback to English
    try {
      const fallbackMessages = await import('../locales/en-US.json');
      return fallbackMessages.default;
    } catch (err) {
      console.error('Failed to load fallback messages', err);
      throw new Error('No valid locale messages available');
    }
  }
}