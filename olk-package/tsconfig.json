{"compilerOptions": {"target": "es2020", "lib": ["es2020", "dom", "dom.iterable", "esnext"], "allowJs": false, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "outDir": "dist", "emitDeclarationOnly": false, "noEmit": false, "baseUrl": "", "paths": {"olk-package": ["types/olk-package.d.ts"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}