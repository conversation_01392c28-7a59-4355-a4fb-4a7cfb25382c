# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
*node_modules/*
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/
/.idea/

# production
/build

# misc
.DS_Store
*.pem
.vscode

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

/olk-package/node_modules
olk-package/dist
