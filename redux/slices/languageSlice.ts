import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface LanguageState {
  value: string;
}

const getCookie = (name: string): string | undefined => {
  const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
  return match ? decodeURIComponent(match[2]) : undefined;
};

const setCookie = (name: string, value: string, days: number) => {
  const expires = new Date();
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
  document.cookie = `${name}=${encodeURIComponent(value)};expires=${expires.toUTCString()};path=/`;
};

const initialState: LanguageState = {
  value: getCookie("NEXT_LOCALE") || "en-US"
};

export const languageSlice = createSlice({
  name: "language",
  initialState,
  reducers: {
    changeLanguage: (state, action: PayloadAction<string>) => {
      state.value = action.payload;
      setCookie("NEXT_LOCALE", state.value, 365); // Set cookie with a 1-year expiration
    }
  }
});

export const { changeLanguage } = languageSlice.actions;
export default languageSlice.reducer;
