import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define a slice for OLK-related state
interface OLKState {
  consignee: string;
  gstin: string;
  address: string;
  selectedParty: any;
}

const initialState: OLKState = {
  consignee: '',
  gstin: '',
  address: '',
  selectedParty: null,
};

const olkSlice = createSlice({
  name: 'olk',
  initialState,
  reducers: {
    setConsignee: (state, action: PayloadAction<string>) => {
      state.consignee = action.payload;
    },
    setGstin: (state, action: PayloadAction<string>) => {
      state.gstin = action.payload;
    },
    setAddress: (state, action: PayloadAction<string>) => {
      state.address = action.payload;
    },
    setSelectedParty: (state, action: PayloadAction<any>) => {
      state.selectedParty = action.payload;
    },
  },
});

export const { setConsignee, setGstin, setAddress, setSelectedParty } = olkSlice.actions;
export default olkSlice.reducer;
