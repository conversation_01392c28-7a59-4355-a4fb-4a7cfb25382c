import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Party {
  custid: number;
  custname: string;
  gstin: string;
  custaddr: string;
  state: string;
  pincode: string;
}

interface Consignee {
  name: string;
  address: string;
  state: string;
  pincode: string;
}

interface Product {
  id: number;
  name: string;
  qty: number;
  rate: number;
  tax: number;
}

interface InvoiceState {
  selectedParty: Party | null;
  consignee: Consignee;
  sameAsBilling: boolean;
  products: Product[];
}

const initialState: InvoiceState = {
  selectedParty: null,
  consignee: {
    name: '',
    address: '',
    state: '',
    pincode: '',
  },
  sameAsBilling: true,
  products: [],
};

const invoiceSlice = createSlice({
  name: 'OLKinvoice',
  initialState,
  reducers: {
    setSelectedParty(state, action: PayloadAction<Party>) {
      state.selectedParty = action.payload;
    },
    setConsignee(state, action: PayloadAction<Consignee>) {
      state.consignee = action.payload;
    },
    setSameAsBilling(state, action: PayloadAction<boolean>) {
      state.sameAsBilling = action.payload;
    },
    setProducts(state, action: PayloadAction<Product[]>) {
      state.products = action.payload;
    },
    addProduct(state, action: PayloadAction<Product>) {
      state.products.push(action.payload);
    },
    updateProduct(state, action: PayloadAction<Product>) {
      const index = state.products.findIndex(p => p.id === action.payload.id);
      if (index !== -1) {
        state.products[index] = action.payload;
      }
    },
    removeProduct(state, action: PayloadAction<number>) {
      state.products = state.products.filter(p => p.id !== action.payload);
    },
    resetInvoice() {
      return initialState;
    },
  },
});

export const {
  setSelectedParty,
  setConsignee,
  setSameAsBilling,
  setProducts,
  addProduct,
  updateProduct,
  removeProduct,
  resetInvoice,
} = invoiceSlice.actions;

export default invoiceSlice.reducer;
