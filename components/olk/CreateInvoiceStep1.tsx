import { useTranslations } from "next-intl";
import { Card } from "primereact/card";
import { FC } from "react";

// Define the interface for props
interface CreateInvoiceStep1Props {
  jwtToken: string;
  locale: string;
}

const CreateInvoiceStep1: FC<CreateInvoiceStep1Props> = ({ jwtToken, locale }) => {
  const t = useTranslations("pages");

  // You can use jwtToken and locale inside this function when needed
  // Example: useEffect(() => { fetchWithToken(jwtToken) }, [])

  return (
    <div id="features" className="my-6 py-6 md:my-8 md:py-8">
      <Card>
        Sample Card - Locale: {locale}
      </Card>
    </div>
  );
};

export default CreateInvoiceStep1;
