.nav_button {
    background: white !important;
    color: #1d4ed8;
    border-color: #1d4ed8;
    padding: 1rem 2rem;
    box-shadow: none;
    font-size: large;
    .pi {
        color: #1d4ed8;
    }

    @media (max-width: 786px) {
        width: 45% !important;
        flex-direction: column;
        gap: 4px;
    }

    @media (max-width: 380px) {
        span:nth-child(2) {
            font-size: 12px !important;
        }
    }
}

.custom-divider {
    height: 0.5px;
    background-color: #1d4ed8;
}

.p-chart {
    height: 250px;
}
.chart-dash {
    width: 80%;
    @media (max-width: 786px) {
        width: 100%;
    }
}

.s-btns {
    @media (max-width: 786px) {
        width: 45% !important;
        justify-content: space-between;
    }
}
