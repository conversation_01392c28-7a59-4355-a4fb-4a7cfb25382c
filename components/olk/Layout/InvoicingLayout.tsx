import { ReactNode } from "react";

type InvoicingLayoutProps = {
  children: ReactNode;
};

const InvoicingLayout = ({ children }: InvoicingLayoutProps) => {
  return (
    <div className="px-4">
      {/* Sticky Header */}
      <div className="sticky top-0 z-50 bg-white">
      </div>

      {/* Page Content */}
      <div className="mt-4 p-5 shadow-md bg-white rounded-2xl">
        {children}
      </div>
    </div>
  );
};

export default InvoicingLayout;
