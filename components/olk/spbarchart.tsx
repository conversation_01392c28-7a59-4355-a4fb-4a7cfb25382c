import { Chart } from 'primereact/chart';
import { useEffect, useState } from 'react';

const SalesPurchaseBarChart = () => {
  const [chartData, setChartData] = useState({});
  const [chartOptions, setChartOptions] = useState({});

  useEffect(() => {
    const data = {
      labels: ['Apr', 'May', 'Jun','Jul','Aug','Sept','Oct','Nov','Dec','Jan', 'Feb', 'Mar'],
      datasets: [
        {
          label: 'Total Sales',
          backgroundColor: '#4ade80', // green (Apollo feel)
          data: [50000, 60000, 45000, 70000, 80000, 75000,100000,40000,50000,10000,300000,40000],
        },
        {
          label: 'Total Purchases',
          backgroundColor: '#60a5fa', // blue
          data: [30000, 35000, 40000, 45000, 50000, 48000,20000,40000,50000,10000,30000,400000],
        },
      ],
    };

    const options = {
      maintainAspectRatio: false,
      responsive: true,
      plugins: {
        legend: {
          labels: {
            color: '#333', // dark grey, readable
            font: {
              size: 12,
              family: 'inherit',
            },
          },
        },
      },
      scales: {
        x: {
          ticks: {
            color: '#555',
            font: {
              size: 12,
            },
          },
          grid: {
            color: '#eee',
          },
        },
        y: {
          ticks: {
            color: '#555',
            font: {
              size: 12,
            },
          },
          grid: {
            color: '#eee',
          },
        },
      }
      ,
    };

    setChartData(data);
    setChartOptions(options);
  }, []);

  return (
    <div className="card p-4 mt-5 shadow-2" style={{ minHeight: '250px' }}>
    <h3 className="text-xl w-10">Monthly Sales vs Purchases</h3>
    <div style={{ height: '100%', width: '100%' }}>
      <Chart type="bar" data={chartData} options={chartOptions} />
    </div>
  </div>
  
  );
};

export default SalesPurchaseBarChart;
