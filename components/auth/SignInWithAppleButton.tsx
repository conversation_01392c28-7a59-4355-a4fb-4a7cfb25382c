import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";
import { Button } from "primereact/button";

export default function SignInWithAppleButton({
    acceptTnc,
}: {
    acceptTnc: boolean;
}) {
    const t = useTranslations("pages.authPage");
    const router = useRouter();

    const setCookie = (name: string, value: string, days: number): void => {
        const expires = new Date(
            Date.now() + days * 24 * 60 * 60 * 1000
        ).toUTCString();
        document.cookie = `${name}=${value}; expires=${expires}; path=/`;
    };

    const handleClick = () => {
        setCookie(
            "backendJwt",
            "eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************.JoP0H055pQmpCeEGVEy-PdMVYgXw6p7iL3wZWZNEHEE",
            365
        );
        router.push("/dashboard/home");
    };

    return (
        <Button
            onClick={handleClick}
            className="flex justify-content-center align-items-center gap-2"
            disabled={!acceptTnc}
        >
            <i className="pi pi-apple"></i>
            <span>{t("continueWithApple")}</span>
        </Button>
    );
}
