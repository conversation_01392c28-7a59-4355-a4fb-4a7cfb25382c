import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";
import { Button } from "primereact/button";

export default function SignInWithGoogleButton({
    acceptTnc,
}: {
    acceptTnc: boolean;
}) {
    const t = useTranslations("pages.authPage");
    const router = useRouter();

    const setCookie = (name: string, value: string, days: number): void => {
        const expires = new Date(
            Date.now() + days * 24 * 60 * 60 * 1000
        ).toUTCString();
        document.cookie = `${name}=${value}; expires=${expires}; path=/`;
    };

    const handleClick = () => {
        setCookie(
            "backendJwt",
            "eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************.JoP0H055pQmpCeEGVEy-PdMVYgXw6p7iL3wZWZNEHEE",
            365
        );
        router.push("/dashboard/home");
    };

    return (
        <Button
            onClick={handleClick}
            className="flex justify-content-center align-items-center gap-2"
            disabled={!acceptTnc}
        >
            <i className="pi pi-google"></i>
            <span>{t("continueWithGoogle")}</span>
        </Button>
    );
}
