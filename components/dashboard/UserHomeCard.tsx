import { Link } from "@/i18n/navigation";
import { useTranslations } from "next-intl";
import { Button } from "primereact/button";
import { Card } from "primereact/card";

export default function UserHomeCard({
    userStatusId,
}: {
    userStatusId: number;
}) {
    const t = useTranslations("pages");
    return (
        <Card>
            <div className="grid grid-nogutter surface-0 text-800">
                <div className="col-12 md:col-6 p-6 text-center md:text-left flex align-items-center">
                    <section>
                        <span className="block text-6xl font-bold mb-1">
                            {userStatusId == 1
                                ? t("homePage.newUserMainHeading1")
                                : t("homePage.existingUserMainHeading1")}
                        </span>
                        <div className="text-6xl text-primary font-bold mb-3">
                            {userStatusId == 1
                                ? t("homePage.newUserMainHeading2")
                                : t("homePage.existingUserMainHeading2")}
                        </div>
                        <p className="mt-0 mb-4 text-700 line-height-3">
                            {userStatusId == 1
                                ? t("homePage.newUserDescription")
                                : t("homePage.existingUserDescription")}
                        </p>
                        {userStatusId == 1 ? (
                            <>
                                <Link href="">
                                    <Button
                                        label={t(
                                            "homePage.completeYourProfileButton"
                                        )}
                                        type="button"
                                        className="mr-3"
                                        raised
                                    />
                                </Link>
                            </>
                        ) : (
                            <>
                                <Link href="">
                                    <Button
                                        label={t("homePage.viewProfileButton")}
                                        type="button"
                                        className="mr-3"
                                        raised
                                    />
                                </Link>
                                <Link href="">
                                    <Button
                                        label={t(
                                            "homePage.exploreSchemesButton"
                                        )}
                                        type="button"
                                        outlined
                                    />
                                </Link>
                            </>
                        )}
                    </section>
                </div>
                <div className="col-12 md:col-6 overflow-hidden">
                    <img
                        src={
                            userStatusId === 1
                                ? "/images/new-user-hero.png"
                                : "/images/hero.png"
                        }
                        alt="Hero Section Image"
                        className="md:ml-auto block md:h-full"
                        style={{
                            clipPath:
                                "polygon(8% 0, 100% 0%, 100% 100%, 0 100%)",
                        }}
                    />
                </div>
            </div>
        </Card>
    );
}
