import { Dropdown } from "primereact/dropdown";
import { useLocale, useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";
import { changeLanguage } from "@/redux/slices/languageSlice";

export default function LanguageDropdown() {

  const t = useTranslations("language");
  const dispatch = useDispatch<AppDispatch>();

  const locale = useLocale();
  const router = useRouter();
  const handleLocaleChange = (newLocale: string) => {
    document.cookie = `NEXT_LOCALE=${newLocale}; path=/; max-age=31536000; SameSite=Lax`;
    localStorage.setItem("locale", newLocale);
    dispatch(changeLanguage(newLocale));
    router.refresh();
  }
  const languageOptions = [
    { label: t("english"), value: "en-US" },
    { label: t("telugu"), value: "te-IN" },
    { label: t("hindi"), value: "hi-IN" },
  ];
  return (
    <Dropdown
      value={locale}
      options={languageOptions}
      optionLabel="label"
      placeholder={t("languageLabel")}
      onChange={(e) => handleLocaleChange(e.target.value)}
      className="w-full md:w-15rem"
    />
  );
}