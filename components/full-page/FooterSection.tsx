import { useTranslations } from "next-intl";

export default function FooterSection() {
  const t = useTranslations("pages");
  return (
    <div className="bg-gray-100/40 surface-border">
      <div className="px-4 py-6">
        <div className="grid">
          {/* Brand Column */}
          <div className="col-12 md:col-6 lg:col-3 mb-4 md:mb-0 flex flex-column align-items-center md:align-items-start">
            <img
              src="/images/logo.png"
              alt="IRIS MSME"
              style={{
                width: "60%",
                height: "75%",
                boxShadow: "0 1px 2px rgba(0,0,0,0.3)",
                borderRadius: "10px"
              }}
            />

          </div>

          {/* Links Columns */}
          <div className="col-12 md:col-6 lg:col-9">
            <div className="grid">
              {/* Links Group */}
              <div className="col-6 md:col-4 lg:col-3">
                <h4 className="font-medium text-xl mb-3 text-900">{t("landingPage.footerHeading1")}</h4>
                <ul className="list-none p-0 m-0">
                  <li className="mb-2">
                    <a className="text-700 hover:text-primary cursor-pointer">
                      {t("landingPage.footerHeading1_1")}
                    </a>
                  </li>
                  <li className="mb-2">
                    <a className="text-700 hover:text-primary cursor-pointer">
                      {t("landingPage.footerHeading1_2")}
                    </a>
                  </li>
                  <li className="mb-2">
                    <a className="text-700 hover:text-primary cursor-pointer">
                      {t("landingPage.footerHeading1_3")}
                    </a>
                  </li>
                </ul>
              </div>

              {/* Policies Group */}
              <div className="col-6 md:col-4 lg:col-3 mt-4 md:mt-0">
                <h4 className="font-medium text-xl mb-3 text-900">{t("landingPage.footerHeading2")}</h4>
                <ul className="list-none p-0 m-0">
                  <li className="mb-2">
                    <a className="text-700 hover:text-primary cursor-pointer">
                      {t("landingPage.footerHeading2_1")}
                    </a>
                  </li>
                  <li className="mb-2">
                    <a className="text-700 hover:text-primary cursor-pointer">
                      {t("landingPage.footerHeading2_2")}
                    </a>
                  </li>
                </ul>
              </div>

              {/* About Group */}
              <div className="col-6 md:col-4 lg:col-3 mt-4 md:mt-0">
                <h4 className="font-medium text-xl mb-3 text-900">{t("landingPage.footerHeading3")}</h4>
                <ul className="list-none p-0 m-0">
                  <li className="mb-2">
                    <a className="text-700 hover:text-primary cursor-pointer">
                      {t("landingPage.footerHeading3_1")}
                    </a>
                  </li>
                  <li className="mb-2">
                    <a className="text-700 hover:text-primary cursor-pointer">
                      {t("landingPage.footerHeading3_2")}
                    </a>
                  </li>
                  <li className="mb-2">
                    <a className="text-700 hover:text-primary cursor-pointer">
                      {t("landingPage.footerHeading3_3")}
                    </a>
                  </li>
                </ul>
              </div>

              {/* Contact Group */}
              <div className="col-6 md:col-12 lg:col-3 mt-4 lg:mt-0">
                <h4 className="font-medium text-xl mb-3 text-900">{t("landingPage.footerHeading4")}</h4>
                <div className="flex flex-column gap-2">
                  <a className="text-700 hover:text-primary cursor-pointer flex align-items-center gap-2">
                    <i className="pi pi-envelope text-sm"></i>
                    {t("landingPage.footerHeading4_1")}
                  </a>
                  <a className="text-700 hover:text-primary cursor-pointer flex align-items-center gap-2">
                    <i className="pi pi-phone text-sm"></i>
                    {t("landingPage.footerHeading4_2")}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright Section */}
        <div className="border-top-1 surface-border mt-5 pt-4 text-center">
          <p className="text-sm text-600">
            © {new Date().getFullYear()} IRIS MSME. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
}