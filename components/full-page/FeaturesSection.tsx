import { useTranslations } from "next-intl";
import { Card } from "primereact/card";

export default function FeaturesSection() {
  const t = useTranslations("pages");
  return (
    <div id="features" className="my-6 py-6 md:my-8 md:py-8">
      <span className="text-900 block font-bold text-5xl mb-4 text-center">
        {t("landingPage.keyBenefitsHeading")}
      </span>
      <span className="text-700 block text-xl mb-8 text-center line-height-3">
        {t("landingPage.keyBenefitsDescription")}
      </span>
      <Card>
        <div className="surface-section px-4 py-8 md:px-6 lg:px-8 text-center">
          <div className="mb-3 font-bold text-3xl">
            <span className="text-900">{t("landingPage.keyBenefitsContentHeading1")}&nbsp;</span>
            <span className="text-blue-600">{t("landingPage.keyBenefitsContentHeading2")}</span>
          </div>
          <div className="text-700 mb-6">
            {t("landingPage.keyBenefitsContentDescription")}
          </div>
          <div className="grid">
            <div className="col-12 md:col-4 mb-4 px-5">
              <span
                className="p-3 shadow-2 mb-3 inline-block"
                style={{ borderRadius: "10px" }}
              >
                <i className="pi pi-search text-4xl text-blue-500"></i>
              </span>
              <div className="text-900 text-xl mb-3 font-medium">
                {t("landingPage.keyBenefit1Heading")}
              </div>
              <span className="text-700 line-height-3">
                {t("landingPage.keyBenefit1Description")}
              </span>
            </div>
            <div className="col-12 md:col-4 mb-4 px-5">
              <span
                className="p-3 shadow-2 mb-3 inline-block"
                style={{ borderRadius: "10px" }}
              >
                <i className="pi pi-comments text-4xl text-blue-500"></i>
              </span>
              <div className="text-900 text-xl mb-3 font-medium">
                {t("landingPage.keyBenefit2Heading")}
              </div>
              <span className="text-700 line-height-3">
                {t("landingPage.keyBenefit2Description")}
              </span>
            </div>
            <div className="col-12 md:col-4 mb-4 px-5">
              <span
                className="p-3 shadow-2 mb-3 inline-block"
                style={{ borderRadius: "10px" }}
              >
                <i className="pi pi-chart-bar text-4xl text-blue-500"></i>
              </span>
              <div className="text-900 text-xl mb-3 font-medium">
                {t("landingPage.keyBenefit3Heading")}
              </div>
              <span className="text-700 line-height-3">
                {t("landingPage.keyBenefit3Description")}
              </span>
            </div>
            <div className="col-12 md:col-4 mb-4 px-5">
              <span
                className="p-3 shadow-2 mb-3 inline-block"
                style={{ borderRadius: "10px" }}
              >
                <i className="pi pi-file text-4xl text-blue-500"></i>
              </span>
              <div className="text-900 text-xl mb-3 font-medium">
                {t("landingPage.keyBenefit4Heading")}
              </div>
              <span className="text-700 line-height-3">
                {t("landingPage.keyBenefit4Description")}
              </span>
            </div>
            <div className="col-12 md:col-4 mb-4 px-5">
              <span
                className="p-3 shadow-2 mb-3 inline-block"
                style={{ borderRadius: "10px" }}
              >
                <i className="pi pi-credit-card text-4xl text-blue-500"></i>
              </span>
              <div className="text-900 text-xl mb-3 font-medium">
                {t("landingPage.keyBenefit5Heading")}
              </div>
              <span className="text-700 line-height-3">
                {t("landingPage.keyBenefit5Description")}
              </span>
            </div>
            <div className="col-12 md:col-4 md:mb-4 mb-0 px-3">
              <span
                className="p-3 shadow-2 mb-3 inline-block"
                style={{ borderRadius: "10px" }}
              >
                <i className="pi pi-cog text-4xl text-blue-500"></i>
              </span>
              <div className="text-900 text-xl mb-3 font-medium">
                {t("landingPage.keyBenefit6Heading")}
              </div>
              <span className="text-700 line-height-3">
                {t("landingPage.keyBenefit6Description")}
              </span>
            </div>
            <div className="col-12 md:col-4 md:mb-4 mb-0 px-3">
              <span
                className="p-3 shadow-2 mb-3 inline-block"
                style={{ borderRadius: "10px" }}
              >
                <i className="pi pi-users text-4xl text-blue-500"></i>
              </span>
              <div className="text-900 text-xl mb-3 font-medium">
                {t("landingPage.keyBenefit7Heading")}
              </div>
              <span className="text-700 line-height-3">
                {t("landingPage.keyBenefit7Description")}
              </span>
            </div>
            <div className="col-12 md:col-4 md:mb-4 mb-0 px-3">
              <span
                className="p-3 shadow-2 mb-3 inline-block"
                style={{ borderRadius: "10px" }}
              >
                <i className="pi pi-user text-4xl text-blue-500"></i>
              </span>
              <div className="text-900 text-xl mb-3 font-medium">
                {t("landingPage.keyBenefit8Heading")}
              </div>
              <span className="text-700 line-height-3">
                {t("landingPage.keyBenefit8Description")}
              </span>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}