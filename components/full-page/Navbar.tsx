"use client";

import { <PERSON> } from "@/i18n/navigation";
import { NodeRef } from "@/types";
import { StyleClass } from "primereact/styleclass";
import { classNames } from "primereact/utils";
import { useRef, useState } from "react";
import LanguageDropdown from "@/components/LanguageDropdown";
import { Ripple } from "primereact/ripple";
import { useTranslations } from "next-intl";
import { Button } from "primereact/button";

export default function FullPageNavbar() {
  const t = useTranslations("pages");
  const menuRef = useRef<HTMLElement | null>(null);
  const [isHidden, setIsHidden] = useState(false);
  const toggleMenuItemClick = () => {
    setIsHidden((prevState) => !prevState);
  };
  return (
    <div className="flex align-items-center justify-content-between relative lg:static py-6 px-4 mx-0 md:px-7 lg:px-8 lg:py-6 lg:mx-8">
      {/* Brand logo */}
      <Link href="/dashboard" className="cursor-pointer">
        <img
          src="/images/svg-logo.svg"
          alt="IRIS MSME"
          style={{
            width: "150px",
            borderRadius: "10px"
          }}
        />
      </Link>

      <StyleClass
        nodeRef={menuRef as NodeRef}
        selector="@next"
        enterClassName="hidden"
        leaveToClassName="hidden"
        hideOnOutsideClick
      >
        <i
          ref={menuRef}
          className="pi pi-bars text-4xl cursor-pointer block md:hidden text-700"
        ></i>
      </StyleClass>

      <div
        className={classNames(
          "align-items-center flex-grow-1 hidden md:flex absolute md:static w-full md:px-0 z-3 shadow-2 md:shadow-none fadein",
          { hidden: isHidden }
        )}
        style={{ top: "80px", right: "0%" }}
      >
        <ul className="list-none p-3 md:p-0 m-0 ml-auto flex md:align-items-center select-none flex-column md:flex-row cursor-pointer surface-card md:surface-ground">
          <li>
            <LanguageDropdown />
          </li>
          <li>
            <a
              href="#home"
              onClick={toggleMenuItemClick}
              className="p-ripple flex m-0 md:ml-5 px-0 py-3 text-900 font-medium line-height-3"
            >
              <span>{t("landingPage.navbarItem1")}</span>
              <Ripple />
            </a>
          </li>
          <li>
            <a
              href="#apps"
              onClick={toggleMenuItemClick}
              className="p-ripple flex m-0 md:ml-5 px-0 py-3 text-900 font-medium line-height-3"
            >
              <span>{t("landingPage.navbarItem2")}</span>
              <Ripple />
            </a>
          </li>
          <li>
            <a
              href="#features"
              onClick={toggleMenuItemClick}
              className="p-ripple flex m-0 md:ml-5 px-0 py-3 text-900 font-medium line-height-3"
            >
              <span>{t("landingPage.navbarItem3")}</span>
              <Ripple />
            </a>
          </li>
          <li>
            <a
              href="#pricing"
              onClick={toggleMenuItemClick}
              className="p-ripple flex m-0 md:ml-5 px-0 py-3 text-900 font-medium line-height-3"
            >
              <span>{t("landingPage.navbarItem4")}</span>
              <Ripple />
            </a>
          </li>
          <li>
            <Button
              type="button"
              label={t("landingPage.navbarItem5")}
              className="m-0 mt-3 md:mt-0 md:ml-5"
            ></Button>
          </li>
          <li>
            {/* <SignInButton /> */}
            <Link href="/auth/login">
              <Button
                type="button"
                label={t("landingPage.navbarItem7")}
                className="m-0 mt-3 md:mt-0 md:ml-5 gap-2"
              />
            </Link>
          </li>
        </ul>
      </div>
    </div>
  );
}