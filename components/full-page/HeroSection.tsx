import { useTranslations } from "next-intl";
import { Card } from "primereact/card";

export default function HeroSection() {
  const t = useTranslations("pages");
  return (
    <Card>
      <div className="grid grid-nogutter surface-0 text-800">
        <div className="col-12 md:col-6 p-6 text-center md:text-left flex align-items-center ">
          <section>
            <span className="block text-6xl font-bold mb-1">
              {t("landingPage.heroSectionHeading1")}
            </span>
            <div className="text-6xl text-primary font-bold mb-3">
              {t("landingPage.heroSectionHeading2")}
            </div>
            <p className="mt-0 mb-4 text-700 line-height-3">
              {t("landingPage.heroSectionDescription")}
            </p>
          </section>
        </div>
        <div className="col-12 md:col-6 overflow-hidden">
          <img
            src={"/images/hero.png"}
            alt="Hero Section Image"
            className="md:ml-auto block md:h-full"
            style={{
              clipPath:
                "polygon(8% 0, 100% 0%, 100% 100%, 0 100%)",
            }}
          />
        </div>
      </div>
    </Card>
  );
}