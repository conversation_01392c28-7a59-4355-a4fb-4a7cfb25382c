{"language": {"languageLabel": "भाषा", "english": "इंग्लिश", "telugu": "तेलुगू (Telugu)", "hindi": "हिन्दी (Hindi)"}, "pages": {"authPage": {"signIn": "साइन इन", "signUp": "साइन अप", "continueWithGoogle": "गूगल के साथ जारी रखें", "continueWithApple": "एप्पल के साथ जारी रखें", "pageDescription": "कृपया अपनी जानकारी दर्ज करें", "emailField": "ईमेल", "passwordField": "पासवर्ड", "confirmPasswordField": "पासवर्ड की पुष्टि करें", "nameField": "नाम", "rememberMe": "मुझे याद रखें", "resetPassword": "पासवर्ड रीसेट करें", "dontHaveAccount": "खाता नहीं है?", "alreadyHaveAccount": "पहले से खाता है?", "agreeToTnc1": "शर्तों और नियमों", "agreeToTnc2": "से सहमत हूँ।"}, "landingPage": {"navbarItem1": "होम", "navbarItem2": "ऐप्स", "navbarItem3": "फीचर्स", "navbarItem4": "मूल्य निर्धारण", "navbarItem5": "अभी खरीदें", "navbarItem6": "गूगल के साथ जारी रखें", "navbarItem7": "साइन इन", "heroSectionHeading1": "आपका स्वागत है", "heroSectionHeading2": "IRIS MSME में", "heroSectionDescription": "जब आपके पास सही उपकरण हों, व्यापार करना आसान हो जाता है। हमारे MSME-अनुकूल उपयोगी उपकरण आपको अनुपालन बनाए रखने, जोखिम प्रबंधन करने, और सूचित निर्णय लेने में मदद करते हैं।", "keyBenefitsHeading": "मुख्य लाभ", "keyBenefitsDescription": "IRIS MSME आपका अंतिम व्यापार साथी है, जिसे चुनौतियों को पार करने और नई ऊंचाइयों तक पहुंचने के लिए डिज़ाइन किया गया है। हमारा व्यापक प्लेटफ़ॉर्म MSME की विशिष्ट आवश्यकताओं के अनुरूप कई लाभ प्रदान करता है।", "keyBenefitsContentHeading1": "अपने व्यवसाय की", "keyBenefitsContentHeading2": "पूर्ण क्षमता खोलें", "keyBenefitsContentDescription": "IRIS MSME आपके व्यापार संचालन को सुव्यवस्थित करने और नए अवसर खोलने के लिए आपका वन-स्टॉप समाधान है।", "keyBenefit1Heading": "योजनाओं का मिलान", "keyBenefit1Description": "अपने व्यापार प्रोफ़ाइल के अनुरूप सरकारी योजनाओं की खोज करें, जिससे अधिकतम लाभ और विकास के अवसर सुनिश्चित हों।", "keyBenefit2Heading": "क्रेडिट तक पहुँच", "keyBenefit2Description": "सुरक्षित डेटा साझा करके बैंक से फंडिंग अनलॉक करें ताकि आपके व्यवसाय के विकास के साथ क्रेडिट सीमाएँ बढ़ सकें।", "keyBenefit3Heading": "एकीकृत अनुपालन", "keyBenefit3Description": "एकीकृत प्लेटफ़ॉर्म का उपयोग करके आसानी से फाइल करें, जो आपके डेटा को ऑटो-फिल करता है, समय बचाता है और त्रुटियाँ कम करता है।", "keyBenefit4Heading": "सरलीकृत अनुपालन", "keyBenefit4Description": "हमारे स्वचालित अनुपालन समाधानों के साथ जटिल नियामक परिदृश्य को आसानी से नेविगेट करें।", "keyBenefit5Heading": "बेहतर वित्तीय स्वास्थ्य", "keyBenefit5Description": "क्रेडिट, ऋण, और बीमा सहित व्यापक वित्तीय सेवाओं तक पहुँच प्राप्त करें।", "keyBenefit6Heading": "संचालित संचालन", "keyBenefit6Description": "हमारे बुद्धिमान उपकरणों के साथ अपने व्यापार प्रक्रियाओं को सुव्यवस्थित करें और उत्पादकता बढ़ाएं।", "keyBenefit7Heading": "विस्तारित बाज़ार पहुंच", "keyBenefit7Description": "हमारे ई-मार्केटप्लेस के माध्यम से बड़े ग्राहक आधार से जुड़ें।", "keyBenefit8Heading": "विशेषज्ञ मार्गदर्शन", "keyBenefit8Description": "सूचित निर्णय लेने के लिए विशेषज्ञ सलाह और समर्थन का लाभ उठाएं।", "footerHeading1": "महत्वपूर्ण लिंक", "footerHeading1_1": "MSME चैम्पियंस", "footerHeading1_2": "उद्यम पंजीकरण", "footerHeading1_3": "पेरिडॉट ऐप डाउनलोड करें", "footerHeading2": "IRIS के नीति", "footerHeading2_1": "गोपनीयता नीति", "footerHeading2_2": "नियम और शर्तें", "footerHeading3": "IRIS के बारे में", "footerHeading3_1": "IRIS IRP", "footerHeading3_2": "IRIS GST", "footerHeading3_3": "IRIS व्यवसाय", "footerHeading4": "संपर्क करें", "footerHeading4_1": "<EMAIL>", "footerHeading4_2": "+91 22 6723 1000"}, "homePage": {"existingUserMainHeading1": "आपका स्वागत है", "existingUserMainHeading2": "IRIS MSME में", "existingUserDescription": "जब आपके पास सही उपकरण हों, व्यापार करना आसान हो जाता है। हमारे MSME-अनुकूल उपकरण आपको अनुपालन, जोखिम प्रबंधन, और सूचित निर्णय लेने में मदद करते हैं।", "viewProfileButton": "प्रोफ़ाइल देखें", "exploreSchemesButton": "योजनाओं का अन्वेषण करें", "newUserMainHeading1": "आपका स्वागत है", "newUserMainHeading2": "IRIS MSME में", "newUserDescription": "आज ही अपना प्रोफ़ाइल पूरा करें और हमारे MSME प्लेटफ़ॉर्म तक पहुँच प्राप्त करें, जो आपको अनुपालन में रखने, जोखिम प्रबंधन में मदद करने, और सूचित निर्णय लेने में सहायक है।", "completeYourProfileButton": "अपना प्रोफ़ाइल पूरा करें"}, "profilePage": {"completeYourProfile": "अपनी प्रोफ़ाइल पूरी करें", "completeYourProfileMessage": "आपकी प्रोफ़ाइल अभी तक नहीं बनाई गई है", "completeYourProfileDescription": "अपनी प्रोफ़ाइल पूरी करने से हमें आपके लिए सबसे उपयुक्त MSME योजनाएँ और अवसर खोजने में मदद मिलती है।", "getStarted": "शुरू करें!", "updateProfileHeading": "अपना प्रोफ़ाइल देखें और अपडेट करें", "form1Heading": "व्यक्तिगत विवरण", "form1Description": "अपनी पहचान सत्यापित करें और एक पूर्ण प्रोफ़ाइल के साथ संबंधित योजनाओं तक आसानी से पहुँचें।", "form2Heading": "पेशेवर विवरण", "form2Description": "आपका व्यवसाय और क्षेत्र हमें उपयुक्त सरकारी लाभों से मेल खाने में मदद करता है।", "form3Heading": "संगठन विवरण", "form3Description": "आपके व्यवसाय का प्रकार और उद्योग उपलब्ध समर्थन और अनुपालन लाभ निर्धारित करते हैं।", "form4Heading": "उद्यम विवरण", "form4Description": "उद्यम पंजीकरण विशेष MSME योजनाएँ और वित्तीय सहायता खोलता है।", "form5Heading": "GSTIN विवरण", "form5Description": "GST पंजीकरण विश्वसनीयता बढ़ाता है और कर लाभ तथा ऋण पहुँच प्रदान करता है।", "form6Heading": "ऋणग्राही विवरण", "form6Description": "आपके पिछले ऋण, डिफॉल्ट, या ब्लैकलिस्टिंग स्थिति हमें आपके लिए उपयुक्त वित्तीय विकल्प खोजने में मदद करती है।", "form7Heading": "उत्पाद/सेवा विवरण", "form7Description": "आपके उत्पादों या सेवाओं का विवरण उपलब्ध बाज़ार अवसरों और योजनाओं की पहचान में मदद करता है।", "form8Heading": "पता विवरण", "form8Description": "सटीक पता जानकारी से क्षेत्र-विशिष्ट योजनाओं की सिफारिशें और अधिकार क्षेत्र आधारित सहायता सुनिश्चित होती है।", "homeButtonLabel": "होम", "registerButtonLabel": "रजिस्टर करें", "updateButtonLabel": "अपडेट करें"}, "schemesPage": {"search": "खोजें", "searchSchemes": "योजनाएँ खोजें...", "noSchemesAvailable": "कोई योजना उपलब्ध नहीं है", "shortlistedSchemes": "शॉर्टलिस्ट की गई योजनाएँ", "allSchemes": "सभी योजनाएँ", "viewAllSchemes": "सभी योजनाएँ देखें", "viewShortlistedSchemes": "शॉर्टलिस्ट की गई योजनाएँ देखें", "schemeDetails": "योजना विवरण", "noSchemeDetailsFound": "योजना विवरण नहीं मिला", "knowMore": "अधिक जानें", "checkEligibility": "पात्रता जाँचें", "objective": "उद्देश्य", "details": "विवरण", "keyBenefits": "मुख्य लाभ", "applicability": "लागू योग्यता", "howToApply": "आवेदन कैसे करें?", "submit": "<PERSON><PERSON><PERSON> करें", "yes": "हाँ", "no": "नहीं", "incompleteForm": "अपूर्ण फ़ॉर्म", "incompleteFormDetail": "कृपया सबमिट करने से पहले सभी प्रश्नों के उत्तर दें", "notEligible": "अयोग्य", "errorEligibility": "त्रुटि", "errorEligibilityDetail": "पात्रता जाँचने में विफल। कृपया बाद में पुनः प्रयास करें", "eligibleMessage": "बधाई हो! 🎉", "eligibleDescription": "आप इस योजना के लिए आवेदन करने के योग्य हैं।", "notEligibleMessage": "ओह!", "notEligibleDescription": "आप इस योजना के लिए आवेदन करने के योग्य नहीं हैं।", "errorDetails": "विवरण"}, "comingSoonPage": {"comingSoon": "जल्द ही आ रहा है", "comingSoonDescription": "रोमांचक सुविधाएँ जल्द ही आ रही हैं। जुड़े रहें!", "goToHomeButton": "होम पर जाएँ"}}, "details": {"personalDetails": {"email": "ईमेल", "name": "नाम", "mobile": "मोबाइल", "gender": "लिंग", "dateOfBirth": "जन्म तिथि", "pan": "PAN", "caste": "जाति", "maritalStatus": "वैवाहिक स्थिति", "educationalQualification": "शैक्षिक योग्यता", "choose": "चुनें"}, "professionalDetails": {"role": "आपको सबसे अच्छा कैसे वर्णित किया जा सकता है?", "stakeInBusiness": "व्यापार में हिस्सेदारी", "occupation": "पेशा", "businessEmail": "व्यावसायिक ईमेल", "choose": "चुनें"}, "entityDetails": {"sector": "क्षेत्र/उद्योग", "isBusinessPANAadhaarSeeded": "क्या व्यावसायिक पैन आधार सीडेड है?", "nameOfBusiness": "व्यापार का नाम", "businessPAN": "व्यापार PAN", "region": "व्यापार क्षेत्र", "jurisdiction": "अधिकार क्षेत्र", "industrialPark": "औद्योगिक पार्क", "industryType": "उद्योग प्रकार", "stateSpecificRegistrationId": "राज्य-विशिष्ट पंजीकरण आईडी", "isUdyamRegistered": "क्या उद्यम पंजीकृत है?", "choose": "चुनें"}, "udyamDetails": {"udyamRegNo": "उद्यम पंजीकरण संख्या", "enterpriseName": "उद्यम का नाम", "ownerName": "माल<PERSON><PERSON> का नाम", "incorporationDate": "संस्थापन तिथि", "commenceDate": "प्रारंभ तिथि", "address": "पता", "pincode": "पिन कोड", "state": "राज<PERSON>य", "district": "जिला", "DIC": "DIC", "employment": "रो<PERSON><PERSON><PERSON><PERSON>", "majorActivity": "मुख्य गतिविधि", "gender": "लिंग", "organisation": "संगठन", "email": "ईमेल", "investmentCost": "निवेश लागत", "netTurnover": "शुद्ध कारोबार", "enterprise": "उद्यम", "NICCode": "NIC कोड", "latitude": "अक्षांश", "longitude": "देशांतर", "yearMonth": "महीना और वर्ष", "choose": "चुनें"}, "gstinDetails": {"gstinNumber": "GSTIN नंबर", "name": "नाम", "legalName": "कानूनी नाम", "dateOfRegistration": "पंजीकरण तिथि", "constitutionOfBusiness": "व्यापार संरचना", "gstnStatus": "GSTIN स्थिति", "dateOfCancellation": "रद्दीकरण तिथि", "einvoiceStatus": "ई-इनवॉइस स्थिति", "einvoiceStatusTrue": "सही", "einvoiceStatusFalse": "गलत", "principalAddress": "प्रमुख पता", "additionalAddresses": "अतिरिक्त पता", "addressLine": "पता पंक्ति", "state": "राज<PERSON>य", "district": "जिला", "pin": "पिन", "addAddress": "पता जोड़ें", "removeAddress": "पता हटाएं", "gstPortalUsername": "GST पोर्टल उपयोगकर्ता नाम", "gstPrimaryEmail": "GST प्राथमिक अधिकृत ईमेल", "gstPrimaryMobile": "GST प्राथमिक अधिकृत मोबाइल", "choose": "चुनें", "approvedStatus": "मंजूर स्थिति", "pendingStatus": "लंबित स्थिति", "rejectedStatus": "अस्वीकृत स्थिति", "viewAdditionalAddresses": "अतिरिक्त पतों को देखें", "noAdditionalAddresses": "कोई अतिरिक्त पते नहीं", "closeAdditionalAddressesDialog": "ब<PERSON><PERSON> करें"}, "borrowerDetails": {"govSubsidyQuestion": "क्या आपने सरकारी सब्सिडी ली है?", "blacklistedQuestion": "क्या आपको सरकार द्वारा ब्लैकलिस्ट किया गया है?", "defaulterQuestion": "क्या आप किसी बैंक के डिफॉल्टर हैं?", "yes": "हाँ", "no": "नहीं"}, "productServiceDetails": {"productServiceHeading": "उत्पाद/सेवा", "hsnOrSac": "HSN या SAC", "nicCode": "NIC कोड", "addMoreNicCode": "नई NIC कोड जोड़ने के लिए Enter या Space दबाएं", "description": "विवरण", "displayName": "प्रदर्शित नाम", "gstRate": "G<PERSON> दर", "measurementUnit": "मापन इकाई", "pricePerUnit": "प्रति यूनिट कीमत", "addProductService": "उत्पाद/सेवा जोड़ें", "choose": "चुनें"}, "addressDetails": {"addressHeading": "पता", "address1": "पता लाइन 1", "address2": "पता लाइन 2", "location": "स्थान", "cityTownVillage": "शहर/कस्बा/गांव", "pinCode": "पिन कोड", "district": "जिला", "state": "राज<PERSON>य", "addressType": "पता प्रकार", "choose": "चुनें", "addAddress": "पता जोड़ें"}}, "layouts": {"sidebar": {"dashboard": {"heading": "डैशबोर्ड", "home": "होम", "myProfile": "मेरा प्रोफ़ाइल", "schemes": "योजनाएँ", "comingSoon": "जल्द आ रहा है"}}, "profileSidebar": {"welcome": "स्वागत है", "logoutHeading": "लॉग आउट", "logoutSubHeading": "अपने खाते से साइन आउट करें"}}, "errors": {"zodErrors": {"customZodErrors": {"invalidPin": "कृपया एक मान्य PIN दर्ज करें", "requiredJurisdiction": "तेलंगाना राज्य में आपका प्राथमिक पता होने के कारण क्षेत्राधिकार आवश्यक है", "requiredStateSpecificRegistrationId": "तेलंगाना राज्य में आपका प्राथमिक पता होने के कारण यह आवश्यक है"}, "userProfileFormErrors": {"name": {"min": "नाम कम से कम 2 अक्षर होना चाहिए", "max": "नाम 100 अक्षरों से अधिक नहीं हो सकता", "alphaOnly": "नाम में केवल अक्षर, स्पेस, हाइफ़न (-), अपॉस्ट्रॉफी (') और पीरियड (.) हो सकते हैं।"}, "email": {"invalid": "कृपया एक मान्य ईमेल पता दर्ज करें"}, "mobile": {"invalidType": "मोबाइल नंबर 10 अंकों का होना चाहिए", "min": "मोबाइल नंबर 10 अंकों का होना चाहिए", "max": "मोबाइल नंबर 10 अंकों का होना चाहिए"}, "genderId": {"invalid": "लिंग आवश्यक है"}, "dateOfBirth": {"invalid": "अमान्य तिथि", "custom": "जन्म तिथि भविष्य में नहीं हो सकती"}, "casteId": {"invalid": "जाति आवश्यक है"}, "PANNumber": {"invalid": "मान्य PAN नंबर होना चाहिए (जैसे, **********)"}}, "professionalProfileFormErrors": {"roleId": {"invalid": "भूमिका आवश्यक है"}, "occupationId": {"invalid": "पेशा आवश्यक है"}, "stakeInBusiness": {"min": "कम से कम 0% होना चाहिए", "max": "100% से अधिक नहीं हो सकता"}}, "entityProfileFormErrors": {"businessPAN": {"invalid": "मान्य PAN नंबर होना चाहिए (जैसे, **********)"}, "nameOfBusiness": {"min": "व्यापार का नाम कम से कम 2 अक्षर होना चाहिए", "max": "व्यापार का नाम 100 अक्षरों से अधिक नहीं हो सकता"}, "jurisdictionId": {"invalid": "अधिकार क्षेत्र आवश्यक है"}, "sectorId": {"invalid": "क्षेत्र आवश्यक है"}, "regionTypeId": {"invalid": "क्षेत्र प्रकार आवश्यक है"}, "addresses": {"min": "कम से कम एक पता आवश्यक है"}}, "udyamProfileFormErrors": {"udyamRegNo": {"invalid": "अमान्य उद्यम पंजीकरण संख्या"}, "enterpriseName": {"invalid": "उद्यम का नाम आवश्यक है"}, "ownerName": {"invalid": "मालिक का नाम आवश्यक है"}, "incorporationDate": {"invalid": "अमान्य तिथि"}, "commenceDate": {"invalid": "अमान्य तिथि"}, "address": {"invalid": "पता आवश्यक है"}, "pincode": {"invalid": "पिन कोड 6 अंकों का होना चाहिए"}, "districtId": {"invalid": "जिला आवश्यक है"}, "stateId": {"invalid": "राज्य आवश्यक है"}, "DIC": {"invalid": "DIC आवश्यक है"}, "employment": {"invalid": "रोजगार संबंधी जानकारी आवश्यक है"}, "majorActivity": {"invalid": "मुख्य गतिविधि आवश्यक है"}, "genderId": {"invalid": "लिंग आवश्यक है"}, "organizationTypeId": {"invalid": "संगठन प्रकार आवश्यक है"}, "emailId": {"invalid": "अमान्य ईमेल प्रारूप"}, "investmentCost": {"invalid": "अमान्य निवेश लागत"}, "netTurnover": {"invalid": "अमान्य शुद्ध कारोबार"}, "enterpriseTypeId": {"invalid": "उद्यम प्रकार आवश्यक है"}, "NICCode": {"invalid": "अमान्य NIC कोड"}}, "gstinProfileFormErrors": {"gstinNumber": {"invalid": "15 अक्षरों की लंबाई होनी चाहिए"}, "name": {"invalid": "नाम आवश्यक है"}, "legalName": {"invalid": "कानूनी नाम आवश्यक है"}, "dateOfRegistration": {"invalid": "अमान्य तिथि"}, "constitutionOfBusiness": {"invalid": "व्यापार संरचना आवश्यक है"}, "gstPortalUsername": {"invalid": "कृपया अपना GST पोर्टल उपयोगकर्ता नाम दर्ज करें"}, "gstPrimaryEmail": {"invalid": "कृपया अपना GST प्राथमिक अधिकृत ईमेल दर्ज करें"}, "gstPrimaryMobile": {"invalid": "मोबाइल नंबर 10 अंकों का होना चाहिए"}}, "borrowerProfileFormErrors": {"availedGovernmentSubsidy": {"required": "कृपया चुनें कि आपने सरकारी सब्सिडी ली है या नहीं", "invalidType": "कृपया मान्य विकल्प चुनें"}, "blacklistedByGovernment": {"required": "कृपया चुनें कि आपको सरकार द्वारा ब्लैकलिस्ट किया गया है या नहीं", "invalidType": "कृपया मान्य विकल्प चुनें"}, "defaulterToBank": {"required": "कृपया चुनें कि आपने किसी बैंक को डिफॉल्ट किया है या नहीं", "invalidType": "कृपया मान्य विकल्प चुनें"}}, "productServiceProfileFormErrors": {"HSNorSAC": {"invalid": "HSN/SAC आवश्यक है"}, "NICCode": {"invalid": "NIC कोड आवश्यक है"}, "description": {"invalid": "विवरण आवश्यक है"}, "displayName": {"invalid": "प्रदर्शित नाम आवश्यक है"}, "GSTRate": {"invalid": "अमान्य GST दर"}, "pricePerUnit": {"invalid": "अमान्य प्रति यूनिट कीमत"}}, "addressProfileFormErrors": {"address1": {"invalid": "पता लाइन 1 आवश्यक है"}, "address2": {"invalid": "पता लाइन 2 आवश्यक है"}, "location": {"invalid": "स्थान आवश्यक है"}, "cityTownVillage": {"invalid": "शहर/कस्बा/गांव आवश्यक है"}, "districtId": {"invalid": "जिला आवश्यक है"}, "pinCode": {"invalid": "पिन कोड 6 अंकों का होना चाहिए"}, "stateId": {"invalid": "राज्य आवश्यक है"}, "addressTypeId": {"invalid": "पता प्रकार आवश्यक है"}}}, "toastErrors": {"badRequest": "खराब अनुरोध। कृपया अपने डेटा की पुष्टि करें।", "somethingWentWrong": "कुछ गलत हो गया"}}, "schemesPage": {"search": "खोजें", "searchSchemes": "योजनाएँ खोजें...", "noSchemesAvailable": "कोई योजना उपलब्ध नहीं है", "shortlistedSchemes": "शॉर्टलिस्ट की गई योजनाएँ", "allSchemes": "सभी योजनाएँ", "viewAllSchemes": "सभी योजनाएँ देखें", "viewShortlistedSchemes": "शॉर्टलिस्ट की गई योजनाएँ देखें", "schemeDetails": "योजना विवरण", "noSchemeDetailsFound": "योजना विवरण नहीं मिला", "knowMore": "अधिक जानें", "checkEligibility": "पात्रता जाँचें", "objective": "उद्देश्य", "details": "विवरण", "keyBenefits": "मुख्य लाभ", "applicability": "लागू योग्यता", "howToApply": "आवेदन कैसे करें?", "submit": "<PERSON><PERSON><PERSON> करें", "yes": "हाँ", "no": "नहीं", "incompleteForm": "अपूर्ण फ़ॉर्म", "incompleteFormDetail": "कृपया सबमिट करने से पहले सभी प्रश्नों के उत्तर दें", "notEligible": "अयोग्य", "errorEligibility": "त्रुटि", "errorEligibilityDetail": "पात्रता जाँचने में विफल। कृपया बाद में पुनः प्रयास करें", "Eligible": "योग्य", "You are eligible for this scheme!": "आप इस योजना के लिए पात्र हैं!", "errornoteligible": "यह योजना आपकी प्रोफ़ाइल के लिए चयनित नहीं है।", "eligibleMessage": "बधाई हो! 🎉", "eligibleDescription": "आप इस योजना के लिए आवेदन करने के योग्य हैं।", "notEligibleMessage": "ओह!", "notEligibleDescription": "आप इस योजना के लिए आवेदन करने के योग्य नहीं हैं।", "errorDetails": "विवरण"}, "success": {"registrationSuccesful": "पंजीकरण सफल रहा", "profileUpdated": "प्रोफ़ाइल अपडेट की गई"}}