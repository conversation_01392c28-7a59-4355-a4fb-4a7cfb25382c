{"language": {"languageLabel": "Language", "english": "English", "telugu": "తెలుగు (Telugu)", "hindi": "हिन्दी (Hindi)"}, "pages": {"authPage": {"signIn": "Sign In", "signUp": "Sign Up", "continueWithGoogle": "Continue with Google", "continueWithApple": "Continue with Apple", "pageDescription": "Please enter your details", "emailField": "Email", "passwordField": "Password", "confirmPasswordField": "Confirm Password", "nameField": "Name", "rememberMe": "Remember Me", "resetPassword": "Reset Password", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "agreeToTnc1": "I agree to the", "agreeToTnc2": "Terms and Conditions"}, "landingPage": {"navbarItem1": "Home", "navbarItem2": "Apps", "navbarItem3": "Features", "navbarItem4": "Pricing", "navbarItem5": "Buy Now", "navbarItem6": "Continue with Google", "navbarItem7": "Sign In", "heroSectionHeading1": "Welcome to", "heroSectionHeading2": "IRIS MSME", "heroSectionDescription": "Running a business is easier when you have the right tools at your fingertips. Our MSME-friendly utilities help you stay compliant, manage risks, and make informed decisions with ease.", "keyBenefitsHeading": "Key Benefits", "keyBenefitsDescription": "IRIS MSME is your ultimate business partner, designed to help you overcome challenges and achieve new heights. Our comprehensive platform offers a range of benefits tailored to the specific needs of MSMEs", "keyBenefitsContentHeading1": "Unlock Your Business's", "keyBenefitsContentHeading2": "Full Potential", "keyBenefitsContentDescription": "IRIS MSME is your one-stop solution to streamline your business operations and unlock new opportunities.", "keyBenefit1Heading": "Schemes Matchmaking", "keyBenefit1Description": "Discover government schemes tailored to your business profile, ensuring maximum benefits and opportunities for growth.", "keyBenefit2Heading": "Access to Credit", "keyBenefit2Description": "Unlock funding from banks by sharing data securely to expand credit limits as your business grows.", "keyBenefit3Heading": "Integrated Compliance", "keyBenefit3Description": "File with ease using an integrated platform that auto-fills your data, saves time, and minimizes errors.", "keyBenefit4Heading": "Simplified Compliance", "keyBenefit4Description": "Effortlessly navigate the complex regulatory landscape with our automated compliance solutions.", "keyBenefit5Heading": "Enhanced Financial Health", "keyBenefit5Description": "Access a wide range of financial services, including credit, loans, and insurance.", "keyBenefit6Heading": "Optimized Operations", "keyBenefit6Description": "Streamline your business processes and boost productivity with our intelligent tools.", "keyBenefit7Heading": "Expanded Market Reach", "keyBenefit7Description": "Connect with a larger customer base through our e-marketplace.", "keyBenefit8Heading": "Expert Guidance", "keyBenefit8Description": "Benefit from expert advice and support to make informed decisions.", "footerHeading1": "Important Links", "footerHeading1_1": "MSME Champions", "footerHeading1_2": "Udyam Registration", "footerHeading1_3": "Download Peridot App", "footerHeading2": "IRIS Policies", "footerHeading2_1": "Privacy Policy", "footerHeading2_2": "Terms and Conditions", "footerHeading3": "About IRIS", "footerHeading3_1": "IRIS IRP", "footerHeading3_2": "IRIS GST", "footerHeading3_3": "IRIS Business", "footerHeading4": "Contact Us", "footerHeading4_1": "<EMAIL>", "footerHeading4_2": "+91 22 6723 1000"}, "homePage": {"existingUserMainHeading1": "Welcome to", "existingUserMainHeading2": "IRIS MSME", "existingUserDescription": "Running a business is easier when you have the right tools at your fingertips. Our MSME-friendly utilities help you stay compliant, manage risks, and make informed decisions with ease.", "viewProfileButton": "View Profile", "exploreSchemesButton": "Explore Schemes", "newUserMainHeading1": "Welcome to", "newUserMainHeading2": "IRIS MSME", "newUserDescription": "Complete your profile today and get access to our MSME platform that will not only help you stay compliant but also in managing risks, making informed decisions with ease and much more.", "completeYourProfileButton": "Complete Your Profile"}, "profilePage": {"completeYourProfile": "Complete Your Profile", "completeYourProfileMessage": "You haven't built your profile yet", "completeYourProfileDescription": "Completing your profile helps us match you with the best MSME schemes and opportunities tailored for you.", "getStarted": "Get Started!", "updateProfileHeading": "View and update your profile", "form1Heading": "Personal Details", "form1Description": "Verify your identity and access relevant schemes easily with a complete profile.", "form2Heading": "Professional Details", "form2Description": "Your occupation and sector help us match you with suitable government benefits.", "form3Heading": "Business Details", "form3Description": "Your business type and industry determine available support and compliance benefits.", "form4Heading": "Udyam Details", "form4Description": "Udyam registration unlocks exclusive MSME schemes and financial aid.", "form5Heading": "GSTIN Details", "form5Description": "GST registration boosts credibility and provides tax benefits and lending access.", "form6Heading": "Credit History", "form6Description": "Your past loans, defaults, or blacklisting status help us find suitable financial options for you.", "form7Heading": "Product Service Details", "form7Description": "Providing details of your products or services helps identify relevant market opportunities and schemes.", "form8Heading": "Address Details", "form8Description": "Accurate address information ensures region-specific scheme recommendations and jurisdiction-based support.", "homeButtonLabel": "Home", "registerButtonLabel": "Register", "updateButtonLabel": "Update"}, "schemesPage": {"search": "Search", "searchSchemes": "Search Schemes...", "noSchemesAvailable": "No Schemes Available", "shortlistedSchemes": "Shortlisted Schemes", "allSchemes": "All Schemes", "viewAllSchemes": "View all Schemes", "viewShortlistedSchemes": "View Shortlisted Schemes", "schemeDetails": "Scheme Details", "noSchemeDetailsFound": "Scheme Details not Found", "knowMore": "Know More", "checkEligibility": "Check Eligibility", "objective": "Objective", "details": "Details", "keyBenefits": "Key Benefits", "applicability": "Applicability", "howToApply": "How to Apply?", "submit": "Submit", "yes": "Yes", "no": "No", "incompleteForm": "Incomplete Form", "incompleteFormDetail": "Please answer all questions before submitting", "notEligible": "Not Eligible", "errorEligibility": "Error", "errorEligibilityDetail": "Failed to check eligibility. Please try again later", "eligibleMessage": "Congratulations! 🎉", "eligibleDescription": "You are eligible to apply for the scheme.", "notEligibleMessage": "Oops!", "notEligibleDescription": "You may not be able to apply for the scheme.", "errorDetails": "Details"}, "comingSoonPage": {"comingSoon": "Coming Soon", "comingSoonDescription": "Exciting features releasing soon. Stay tuned!", "goToHomeButton": "Go to Home"}}, "details": {"personalDetails": {"email": "Email", "name": "Name", "mobile": "Mobile", "gender": "Gender", "dateOfBirth": "Date of Birth", "pan": "PAN", "caste": "<PERSON><PERSON>", "maritalStatus": "Marital Status", "educationalQualification": "Educational Qualification", "choose": "<PERSON><PERSON>"}, "professionalDetails": {"role": "What describes you best?", "stakeInBusiness": "Stake in business", "occupation": "Occupation", "businessEmail": "Business Email", "choose": "<PERSON><PERSON>"}, "entityDetails": {"sector": "Sector/Industry", "isBusinessPANAadhaarSeeded": "Is Business PAN Aadhaar seeded?", "nameOfBusiness": "Name of Business", "businessPAN": "Business PAN", "region": "Business Region", "jurisdiction": "Juris<PERSON>", "industrialPark": "Industrial Park", "industryType": "Industry Type", "stateSpecificRegistrationId": "State Specific Registration ID", "isUdyamRegistered": "Is Udyam Registered?", "choose": "<PERSON><PERSON>"}, "udyamDetails": {"udyamRegNo": "Udyam Registration Number", "enterpriseName": "Enterprise Name", "ownerName": "Owner Name", "incorporationDate": "Incorporation Date", "commenceDate": "Commence Date", "address": "Address", "pincode": "PIN Code", "state": "State", "district": "District", "DIC": "DIC", "employment": "Employment", "majorActivity": "Major Activity", "gender": "Gender", "organisation": "Organisation", "email": "Email", "investmentCost": "Investment Cost", "netTurnover": "Net Turnover", "enterprise": "Enterprise", "NICCode": "NIC Code", "latitude": "Latitude", "longitude": "Longitude", "yearMonth": "Month and Year", "choose": "<PERSON><PERSON>"}, "gstinDetails": {"gstinNumber": "GSTIN Number", "name": "Name", "legalName": "Legal Name", "dateOfRegistration": "Date of Registration", "constitutionOfBusiness": "Constitution of Business", "gstnStatus": "GSTIN Status", "dateOfCancellation": "Date of Cancellation", "einvoiceStatus": "E-Invoice Status", "einvoiceStatusTrue": "True", "einvoiceStatusFalse": "False", "principalAddress": "Principal Address", "additionalAddresses": "Additional Address", "addressLine": "Address Line", "state": "State", "district": "District", "pin": "PIN", "addAddress": "Add Address", "removeAddress": "Remove Address", "gstPortalUsername": "GST Portal Username", "gstPrimaryEmail": "GST Primary Authorised Email", "gstPrimaryMobile": "GST Primary Authorised Mobile", "choose": "<PERSON><PERSON>", "approvedStatus": "APPROVED", "pendingStatus": "PENDING", "rejectedStatus": "REJECTED", "viewAdditionalAddresses": "View Additional Addresses", "noAdditionalAddresses": "No Additional Addresses", "closeAdditionalAddressesDialog": "Close"}, "borrowerDetails": {"govSubsidyQuestion": "Have you availed government subsidy?", "blacklistedQuestion": "Have you been blacklisted by the government?", "defaulterQuestion": "Are you a defaulter to any bank?", "yes": "Yes", "no": "No"}, "productServiceDetails": {"productServiceHeading": "Product Service", "hsnOrSac": "HSN or SAC", "nicCode": "NIC Code", "addMoreNicCode": "Press Enter or Space to add new NIC codes.", "description": "Description", "displayName": "Display Name", "gstRate": "GST Rate", "measurementUnit": "Unit of Measurement", "pricePerUnit": "Price per Unit", "addProductService": "Add Product Service", "choose": "<PERSON><PERSON>"}, "addressDetails": {"addressHeading": "Address", "address1": "Address Line 1", "address2": "Address Line 2", "location": "Location", "cityTownVillage": "City/Town/Village", "pinCode": "PIN Code", "district": "District", "state": "State", "addressType": "Address Type", "choose": "<PERSON><PERSON>", "addAddress": "Add Address"}}, "layouts": {"sidebar": {"dashboard": {"heading": "Dashboard", "home": "Home", "myProfile": "My Profile", "schemes": "Schemes", "comingSoon": "Coming Soon"}}, "profileSidebar": {"welcome": "Welcome", "logoutHeading": "Log Out", "logoutSubHeading": "Sign out of your account"}}, "errors": {"zodErrors": {"customZodErrors": {"invalidPin": "Please enter a valid PIN", "requiredJurisdiction": "Jurisdiction is required since your primary address is in the state of Telangana", "requiredStateSpecificRegistrationId": "It is required since your primary address is in the state of Telangana"}, "userProfileFormErrors": {"name": {"min": "Name is required", "max": "Name cannot exceed 100 characters", "alphaOnly": "Name can only contain letters, spaces, hyphens (-), apostrophes ('), and periods (.)"}, "email": {"invalid": "Please enter a valid email address"}, "mobile": {"invalidType": "Must be a 10-digit mobile number", "min": "Must be a 10-digit mobile number", "max": "Must be a 10-digit mobile number"}, "genderId": {"invalid": "Gender is required"}, "dateOfBirth": {"invalid": "Invalid Date", "custom": "Date of birth cannot be in the future"}, "casteId": {"invalid": "Caste is required"}, "PANNumber": {"invalid": "Must be a valid PAN number (e.g., **********)"}}, "professionalProfileFormErrors": {"roleId": {"invalid": "Role is required"}, "occupationId": {"invalid": "Occupation is required"}, "stakeInBusiness": {"min": "Must be at least 0%", "max": "Cannot exceed 100%"}}, "entityProfileFormErrors": {"businessPAN": {"invalid": "Must be a valid PAN number (e.g., **********)"}, "nameOfBusiness": {"min": "Business name is required", "max": "Business name cannot exceed 100 characters"}, "jurisdictionId": {"invalid": "Jurisdiction is required"}, "sectorId": {"invalid": "Sector is required"}, "regionTypeId": {"invalid": "Region type is required"}, "addresses": {"min": "At least one address is required"}}, "udyamProfileFormErrors": {"udyamRegNo": {"invalid": "Invalid Udyam registration number"}, "enterpriseName": {"invalid": "Enterprise name is required"}, "ownerName": {"invalid": "Owner name is required"}, "incorporationDate": {"invalid": "Invalid Date"}, "commenceDate": {"invalid": "Invalid Date"}, "address": {"invalid": "Address is required"}, "pincode": {"invalid": "Must be a 6-digit pincode"}, "districtId": {"invalid": "District is required"}, "stateId": {"invalid": "State is required"}, "DIC": {"invalid": "DIC is required"}, "employment": {"invalid": "Employment information is required"}, "majorActivity": {"invalid": "Major activity is required"}, "genderId": {"invalid": "Gender is required"}, "organizationTypeId": {"invalid": "Organization type is required"}, "emailId": {"invalid": "Invalid email format"}, "investmentCost": {"invalid": "Invalid investment cost"}, "netTurnover": {"invalid": "Invalid net turnover"}, "enterpriseTypeId": {"invalid": "Enterprise type is required"}, "NICCode": {"invalid": "Invalid NIC code"}}, "gstinProfileFormErrors": {"gstinNumber": {"invalid": "Must be 15 characters long"}, "name": {"invalid": "Name is required"}, "legalName": {"invalid": "Legal name is required"}, "dateOfRegistration": {"invalid": "Invalid Date"}, "constitutionOfBusiness": {"invalid": "Constitution of business is required"}, "gstPortalUsername": {"invalid": "Please enter your GST portal username"}, "gstPrimaryEmail": {"invalid": "Please enter your GST primary authorised email"}, "gstPrimaryMobile": {"invalid": "Must be a 10-digit mobile number"}}, "borrowerProfileFormErrors": {"availedGovernmentSubsidy": {"required": "Please select if you've availed government subsidy", "invalidType": "Please select a valid option"}, "blacklistedByGovernment": {"required": "Please select if blacklisted by government", "invalidType": "Please select a valid option"}, "defaulterToBank": {"required": "Please select if you've defaulted to any bank", "invalidType": "Please select a valid option"}}, "productServiceProfileFormErrors": {"HSNorSAC": {"invalid": "HSN/SAC is required"}, "NICCode": {"invalid": "NIC code is required"}, "description": {"invalid": "Description is required"}, "displayName": {"invalid": "Display name is required"}, "GSTRate": {"invalid": "Invalid GST rate"}, "pricePerUnit": {"invalid": "Invalid price per unit"}}, "addressProfileFormErrors": {"address1": {"invalid": "Address line 1 is required"}, "address2": {"invalid": "Address line 2 is required"}, "location": {"invalid": "Location is required"}, "cityTownVillage": {"invalid": "City/Town/Village is required"}, "districtId": {"invalid": "District is required"}, "pinCode": {"invalid": "Must be a 6-digit pincode"}, "stateId": {"invalid": "State is required"}, "addressTypeId": {"invalid": "Address type is required"}}}, "toastErrors": {"badRequest": "Bad Request. Please verify your data.", "somethingWentWrong": "Something went wrong"}}, "schemesPage": {"search": "Search", "searchSchemes": "Search Schemes...", "noSchemesAvailable": "No Schemes Available", "shortlistedSchemes": "Shortlisted Schemes", "allSchemes": "All Schemes", "viewAllSchemes": "View all Schemes", "viewShortlistedSchemes": "View Shortlisted Schemes", "schemeDetails": "Scheme Details", "noSchemeDetailsFound": "Scheme Details not Found", "knowMore": "Know More", "checkEligibility": "Check Eligibility", "objective": "Objective", "details": "Details", "keyBenefits": "Key Benefits", "applicability": "Applicability", "howToApply": "How to Apply?", "submit": "Submit", "yes": "Yes", "no": "No", "incompleteForm": "Incomplete Form", "incompleteFormDetail": "Please answer all questions before submitting", "notEligible": "Not Eligible", "errorEligibility": "Error", "errorEligibilityDetail": "Failed to check eligibility. Please try again later", "Eligible": "Eligible", "You are eligible for this scheme!": "You are eligible for this scheme!", "errornoteligible": "This scheme is not shortlisted for your profile.", "eligibleMessage": "Congratulations! 🎉", "eligibleDescription": "You are eligible to apply for the scheme.", "notEligibleMessage": "Oops!", "notEligibleDescription": "You may not be able to apply for the scheme.", "errorDetails": "Details"}, "success": {"registrationSuccesful": "Registration successful", "profileUpdated": "Profile Updated"}}