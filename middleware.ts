import createMiddleware from 'next-intl/middleware';
import {routing} from '@/i18n/routing';
 
export default createMiddleware(routing);
 
// middleware.ts
export const config = {
  matcher: ['/((?!api|_next|.*\\..*).*)']
};




// import { NextResponse } from "next/server";
// import type { NextRequest } from "next/server";
// import { auth } from "@/auth";

// export default async function middleware(request: NextRequest) {
//   // const session = await auth();

//   // const pathname = request.nextUrl.pathname;
//   // const isProtected = pathname === "/dashboard/register";

//   // const url = request.nextUrl.clone();
//   // url.pathname = "/";
//   // url.search = "";
//   // if (isProtected && !session) {
//   //   return NextResponse.redirect(url);
//   // }
//   // else {
//   //   return NextResponse.next();
//   // }
// }