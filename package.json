{"name": "apollo-react", "version": "10.1.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "next dev -p 8080", "build": "next build", "start": "next start -p 8080", "format": "prettier --write \"{app,demo,layout,types}/**/*.{js,ts,tsx,d.ts}\"", "lint": "next lint"}, "dependencies": {"@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/interaction": "^6.1.8", "@fullcalendar/react": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@hookform/resolvers": "^3.10.0", "@react-pdf/renderer": "^4.3.0", "@reduxjs/toolkit": "^2.8.0", "@types/node": "20.3.1", "@types/react": "^18.3.18", "@types/react-dom": "18.2.5", "axios": "^1.7.9", "chart.js": "4.2.1", "cookies-next": "^5.1.0", "decimal.js": "^10.5.0", "eslint-plugin-react": "^7.37.5", "firebase": "^11.4.0", "js-cookie": "^3.0.5", "next": "^15.3.0", "next-intl": "^3.26.5", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "primereact": "^10.2.1", "quill": "^1.3.7", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.56.1", "react-pdf": "^9.2.1", "react-redux": "^9.2.0", "typescript": "5.1.3", "zod": "^3.24.4"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "eslint": "^9.26.0", "eslint-config-next": "^15.3.1", "nodemon": "^3.1.10", "prettier": "^2.8.8", "sass": "^1.63.4"}}