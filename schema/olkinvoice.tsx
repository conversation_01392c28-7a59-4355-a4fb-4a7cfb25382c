// schema/invoice.ts
import { string, z } from 'zod';

// Individual product schema
export const productSchema = z.object({
  productcode: z.number(),
  productname: z.string(),
  quantity: z.number().min(1),
  freeQuantity: z.number().nonnegative(),
  gstflag: z.number(),
  gstrate: z.number(),
  pricePerUnit: z.number().nonnegative(),
  gstamount: z.number().nonnegative(),
  taxableAmount: z.number().nonnegative(),
  productAmount: z.number().nonnegative(),
  gsflag: z.number(),
  discountAmount: z.number().nonnegative(),
});


const invoiceNoRegex = /^(?![0\-\/])[A-Za-z0-9\-\/]{1,16}$/;
const TransportationModeEnum = z.enum(['None', 'By Road', 'Ship', 'Rail', 'Air', 'Other']);
const bankDetailsSchema = z.object({
  accountno: z.string().min(1, "Account number is required"),
  accountname: z.string().min(1, "Account name is required"),
  ifs: z.string().min(1, "IFSC code is required"),
  bankname: z.string().min(1, "Bank name is required"),
});

const paymentModeMap: Record<string, number> = {
  Cash: 3,
  Bank: 2,
  Online: 2,
  'On Credit': 15,
};

// Define consignee schema
const consigneeSchema = z.object({
  name: z.string().min(1, "Consignee name is required"),
  address: z.string(),
  state: z.string(),
  pincode: z.string(),
});


export const invoicePayloadSchema = z.object({
  icflag: z.union([z.literal(9), z.literal(3)]),
  inoutflag: z.union([z.literal(9), z.literal(15)]),
  invoiceno: z.union([
    z.string().regex(invoiceNoRegex, "Invalid invoice number format"),
    z.number()
  ]),
  contents: z.array(productSchema).min(1),
  roundoffflag: z.number().optional().default(0),
  invoicetotal: z.number().nonnegative().default(0.00),
  amountpaid: z.number().nonnegative().default(0.00),
  invoicedate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
  partyname: z.string(),
  custid: z.number(),
  orgcode: z.number(),
  discflag: z.union([z.literal(1), z.literal(16)]),
  transportationmode: TransportationModeEnum.optional(),
  vehicleno:z.string().optional(),
  dateofsupply: z.union([z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),z.literal(''),z.null()]).optional(),  
  bankdetails: bankDetailsSchema.optional(),
  consignee: consigneeSchema.optional(),
  taxstate:z.string(),
  sourcestate:z.string(),
  invnarration:z.string().optional(),
  orgstategstin:z.string(),
  invoicetotalword:z.string(),
  paymentmode: z.number(),
  });

export type InvoicePayload = z.infer<typeof invoicePayloadSchema>;
export type InvoiceProduct = z.infer<typeof productSchema>;
export type TransportationModeType = z.infer<typeof TransportationModeEnum>;
