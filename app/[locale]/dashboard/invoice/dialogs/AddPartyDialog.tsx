'use client';

import React from 'react';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import {AddPartyForm} from '@iris-olk/invoice';

interface AddPartyDialogProps {
    visible: boolean;
    onHide: () => void;
}

const AddPartyDialog: React.FC<AddPartyDialogProps> = ({ visible, onHide }) => {
    return (
        <Dialog header="Add Party" visible={visible} style={{ width: '30vw' }} onHide={onHide} modal>
            <AddPartyForm onHide={onHide} />
        </Dialog>
    );
};

export default AddPartyDialog;