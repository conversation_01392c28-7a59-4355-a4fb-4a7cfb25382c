'use client';

import React from 'react';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';

interface AddProdDialogProps {
    visible: boolean;
    onHide: () => void;
}

const AddProdDialog: React.FC<AddProdDialogProps> = ({ visible, onHide }) => {
    return (
        <Dialog header="Add Party" visible={visible} style={{ width: '30vw' }} onHide={onHide} modal>
            <div className="flex flex-col gap-3">
                <span className="p-float-label">
                    <InputText id="name" />
                    <label htmlFor="name">Name</label>
                </span>
                <span className="p-float-label">
                    <InputText id="email" />
                    <label htmlFor="email">Email</label>
                </span>
                <span className="p-float-label">
                    <InputText id="phone" />
                    <label htmlFor="phone">Phone</label>
                </span>
                <div className="flex justify-end mt-4">
                    <Button label="Save" icon="pi pi-check" className="p-button-success" />
                </div>
            </div>
        </Dialog>
    );
};

export default AddProdDialog;
