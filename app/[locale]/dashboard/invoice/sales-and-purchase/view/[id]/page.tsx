
// app/[locale]/dashboard/olkdashboard/invoice/view/[id]/page.tsx
'use client';
import React from 'react';
import { use } from 'react';
import {InvoiceView } from '@iris-olk/invoice';
import '@/styles/olk/olkcss.scss';

interface InvoiceViewPageProps {
  params: Promise<{ id: string }>;
}

const InvoiceViewPage: React.FC<InvoiceViewPageProps> = ({ params }) => {
  // Unwrap the params Promise using React.use
  const { id } = use(params);
  console.log(id);

  return (
    <div className="invoice-view-container">
      <InvoiceView invoiceId={id} />
    </div>
  );
};

export default InvoiceViewPage;