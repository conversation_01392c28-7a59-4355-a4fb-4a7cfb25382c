"use client";
import React from "react";
import { useSearchParams } from "next/navigation";
import { PaymentReceiptForm } from "@iris-olk/invoice";

/**
 * Page for entering payment/receipt for an invoice.
 * Passes invoiceId and invoiceType from query params to the form.
 */
const PaymentReceiptFormPage = () => {
    const searchParams = useSearchParams();
    const invoiceId = searchParams.get("invoiceId") || undefined;
    const invoiceType =
        (searchParams.get("invoiceType") as "sales" | "purchase") || undefined;

    return (
        <PaymentReceiptForm invoiceId={invoiceId} invoiceType={invoiceType} />
    );
};

export default PaymentReceiptFormPage;
