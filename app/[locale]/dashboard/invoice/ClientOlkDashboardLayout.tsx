// app/[locale]/ClientOlkDashboardLayout.tsx
"use client";

import { Toast } from "primereact/toast";
import { useRef } from "react";
import type { ReactNode } from "react";
import { NextIntlClientProvider } from "next-intl";
import {OrgHeader,BackButton} from "@iris-olk/invoice";

interface Props {
  children: ReactNode;
  locale: string;
  messages: Record<string, string>; // Improved type for messages
}

export default function ClientOlkDashboardLayout({
  children,
  locale,
  messages,
}: Props) {
  const toast = useRef(null);

  return (
    <NextIntlClientProvider locale={locale} messages={messages}>
      <Toast ref={toast} />
      <OrgHeader/> {/* Custom fallback */}
            <BackButton/>

      <div className="">{children}</div>
    </NextIntlClientProvider>
  );
}