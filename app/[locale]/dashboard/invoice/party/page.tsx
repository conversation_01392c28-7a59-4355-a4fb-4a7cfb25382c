"use client";

import { TabView, TabPanel } from "primereact/tabview";
import {AddPartyForm} from "@iris-olk/invoice";
import {PartyListTable} from "@iris-olk/invoice";

const PartyScreen = () => {
  return (

    <div className="card p-0 sm:p-4 shadow-2 rounded-lg custom-tabview relative z-">
    <TabView>
      <TabPanel header="Add Party">
      <AddPartyForm onHide={() => {}} />
              </TabPanel>
      <TabPanel header="All Parties">
        <PartyListTable />
      </TabPanel>
    </TabView>
  </div>
    
  );
};

export default PartyScreen;