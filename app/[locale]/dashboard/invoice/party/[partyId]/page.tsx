"use client";

import React from "react";
import { PendingInvoicesTable } from "@iris-olk/invoice";
import { useSearchParams } from "next/navigation";

const Page = () => {
    const searchParams = useSearchParams();
    const type = searchParams.get("type");

    // Redirect or show error if type is not provided
    if (!type || (type !== "receipts" && type !== "payments")) {
        return (
            <div className="card p-4">
                <h2 className="text-xl font-bold text-red-500">Error</h2>
                <p>
                    Invalid or missing type parameter. Please specify either receipts or payments.
                </p>
            </div>
        );
    }

    return (
        <>
            <PendingInvoicesTable />
        </>
    );
};

export default Page;
