// import React, { ReactNode } from "react";
// import { OrgProvider } from '@/context/orgContext';

// export default function OlkDashboardLayout({ children }: { children: ReactNode }) {
//   const companyName = "Bookmatic PVT LTD"; // TODO: Replace with dynamic API call

//     return (
//       <OrgProvider>
//         <div className="px-2 py-1 sm:px-2 sm:py-2">
//           <div className="sticky top-0 z-10 bg-white card px-3 py-1 sm:px-4 sm:py-2 shadow-2 rounded-md mb-2 sm:mb-4">
//             <h3 className="text-2xl sm:text-3xl text-primary font-bold m-0">{companyName}</h3>
//           </div>
//           <div className="mt-2 sm:mt-4">
//             {children} {/* ✅ now actually wrapped in OrgProvider */}
//           </div>
//         </div>
//       </OrgProvider>
//     );
//   }

// app/[locale]/layout.tsx (Server Component)
import { notFound } from "next/navigation";
import { OrgProvider } from "@/context/orgContext";
import type { ReactNode } from "react";
import ClientOlkDashboardLayout from "./ClientOlkDashboardLayout";

type Props = {
    children: ReactNode;
    params: Promise<{ locale: string }>;
};

async function getMessages(locale: string) {
    try {
        return (await import(`@/locales/${locale}.json`)).default;
    } catch (error) {
        notFound();
    }
}

export default async function OlkDashboardLayout({ children, params }: Props) {
    const { locale } = await params; // Await params in Server Component
    const messages = await getMessages(locale); // Fetch messages in Server Component

    return (
        <OrgProvider>
            <ClientOlkDashboardLayout locale={locale} messages={messages}>
                {children}
            </ClientOlkDashboardLayout>
        </OrgProvider>
    );
}