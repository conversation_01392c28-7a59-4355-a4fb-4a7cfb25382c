"use client";
import { TabView, TabPanel } from "primereact/tabview";
import { AddPSForm, ProdListTable } from "@iris-olk/invoice";
import { useRef } from "react";
import { Toast } from "primereact/toast";

const ProductScreen = () => {
    const toastRef = useRef<Toast>(null); // Create a Toast ref

    return (
        <div className="card p-0 sm:p-4 shadow-2 rounded-lg custom-tabview">
            <TabView>
                <TabPanel header="Add Product and Service">
                    <AddPSForm onHide={() => {}} toastMsg={toastRef} />
                </TabPanel>
                <TabPanel header="All Products and Services">
                    <ProdListTable />
                </TabPanel>
            </TabView>
        </div>
    );
};

export default ProductScreen;
