"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { PartyPendingList } from "@iris-olk/invoice";

export default function PendingListPage() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const type = searchParams.get("type") as "receipts" | "payments";

    if (!type) {
        return <div>Invalid type parameter</div>;
    }

    return (
        <PartyPendingList
            type={type}
            onClose={() => router.push("/dashboard/invoice")}
        />
    );
}
