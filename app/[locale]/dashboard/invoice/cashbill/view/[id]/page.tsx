"use client";
import React from "react";
import { use } from "react";
import { CashBillView } from "@iris-olk/invoice";
import "@/styles/olk/olkcss.scss";

// Cash Bill View Page
// This page displays detailed view of a specific cash bill.
// It shows all cash bill information, products, and financial summary.

interface CashBillViewPageProps {
    params: Promise<{ id: string }>;
}

const CashBillViewPage: React.FC<CashBillViewPageProps> = ({ params }) => {
    // Unwrap the params Promise using React.use
    const { id } = use(params);
    // console.log("Cash Bill ID:", id);

    return (
        <div className="cash-bill-view-container">
            {/* <h1>This is the Cash Bill View Page</h1> */}
            <CashBillView cashBillId={id}/>
        </div>
    );
};

export default CashBillViewPage;
