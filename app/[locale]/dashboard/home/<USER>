"use client";

import UserHomeCard from "@/components/dashboard/UserHomeCard";
import CreateInvoiceStep1 from "@/components/olk/CreateInvoiceStep1";

// Dummy values for jwtToken and locale
const jwtToken = "your-jwt-token-goes-here";
const locale = "en-US";

export default function Home() {
    const userStatusId = 1;

    if (userStatusId === null || userStatusId === undefined) {
        return <i className="pi pi-spin pi-spinner text-2xl"></i>;
    }

    return (
        <div className="flex flex-column gap-4">
            <UserHomeCard userStatusId={userStatusId} />
            <CreateInvoiceStep1 jwtToken={jwtToken} locale={locale} />
        </div>
    );
}
