import type { Page } from "@/types";
import Navbar from "@/components/full-page/Navbar";
import HeroSection from "@/components/full-page/HeroSection";
import FeaturesSection from "@/components/full-page/FeaturesSection";
import FooterSection from "@/components/full-page/FooterSection";
import BackgroundIllustrations from "@/components/background-illustrations/LandingPage";

const LandingPage: Page = () => {
  return (
    <div className="relative overflow-hidden flex flex-column justify-content-center">
      <BackgroundIllustrations />
      <div className="landing-wrapper">
        <Navbar />
        <div className="py-4 px-4 mx-0 md:mx-6 lg:mx-8 lg:px-8 z-2">
          <HeroSection />
          <FeaturesSection />
          <FooterSection />
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
