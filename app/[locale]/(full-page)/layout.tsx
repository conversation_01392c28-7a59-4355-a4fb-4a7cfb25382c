import { Metadata } from "next";
import React from "react";
import AppConfig from "@/layout/AppConfig";

interface FullPageLayoutProps {
  children: React.ReactNode;
}

export const metadata: Metadata = {
  title: "Welcome to IRIS MSME",
  description:
    "One Platform, Multiple utilities for MSMEs.",
};

export default function FullPageLayout({ children }: FullPageLayoutProps) {
  return (
    <React.Fragment>
      {children}
      <AppConfig minimal />
    </React.Fragment>
  );
}
