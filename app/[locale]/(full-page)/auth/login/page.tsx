"use client";
import type { Page } from "@/types";
import { Checkbox } from "primereact/checkbox";
import { useState } from "react";
import { Link } from "@/i18n/navigation";
import { useTranslations } from "next-intl";
import LanguageDropdown from "@/components/LanguageDropdown";
import SignInWithGoogleButton from "@/components/auth/SignInWithGoogleButton";
import SignInWithAppleButton from "@/components/auth/SignInWithAppleButton";
import BackgroundIllustrations from "@/components/background-illustrations/AuthPage";

const Login: Page = () => {
  const t = useTranslations("pages.authPage");
  const [acceptTnc, setAcceptTnc] = useState(false);
  return (
    <>
      <BackgroundIllustrations />
      <div className="px-5 min-h-screen flex justify-content-center align-items-center">
        <div className="border-1 surface-border surface-card border-round pt-0 pb-5 px-4 md:px-7 z-1">
          <Link href="/dashboard" className="cursor-pointer">
            <img
              src="/images/svg-logo.svg"
              alt="IRIS MSME"
              width={100}
            />
          </Link>
          <div className="mb-4">
            <div className="flex justify-content-between align-items-center gap-4 text-900 text-xl font-bold mb-2">
              {t("signIn")}
              <LanguageDropdown />
            </div>
          </div>
          <div className="flex flex-column">
            {/* accept tnc */}
            <div className="mb-4 flex flex-wrap gap-3">
              <div>
                <Checkbox
                  name="tnc-checkbox"
                  checked={acceptTnc}
                  onChange={(e) =>
                    setAcceptTnc(e.checked ?? false)
                  }
                  className="mr-2"
                ></Checkbox>
                <label
                  htmlFor="tnc-checkbox"
                  className="text-900 font-medium mr-8"
                >
                  <span className="text-600">{t("agreeToTnc1")}</span>{" "}
                  <a href="https://www.irisbusiness.com/terms-and-conditions/" className="font-semibold cursor-pointer text-900 hover:text-primary transition-colors transition-duration-300" target="_blank">{t("agreeToTnc2")}</a>
                </label>
              </div>
            </div>
            <div className="flex flex-column gap-3">
              <SignInWithGoogleButton acceptTnc={acceptTnc} />
              <SignInWithAppleButton acceptTnc={acceptTnc} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Login;
