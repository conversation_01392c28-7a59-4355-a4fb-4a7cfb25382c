import { LayoutProvider } from "@/layout/context/layoutcontext";
import "primeflex/primeflex.css";
import "primeicons/primeicons.css";
import { PrimeReactProvider } from "primereact/api";
import "primereact/resources/primereact.css";
import "@/styles/demo/Demos.scss";
import "@/styles/layout/layout.scss";
import { StoreProvider } from "@/providers/ReduxStoreProvider";
import { getMessages } from "next-intl/server";
import { NextIntlClientProvider } from "next-intl";
import { routing } from "@/i18n/routing";
import { notFound } from "next/navigation";
import { Metadata } from "next";
import { EnvProvider } from "@/providers/EnvContextProvider";
import { env } from "@/config/constants";
import { OlkProvider } from "@iris-olk/invoice";

export const metadata: Metadata = {
    title: "Welcome to IRIS MSME",
    description: "One Platform, Multiple utilities for MSMEs.",
};

interface RootLayoutProps {
    children: React.ReactNode;
    params: Promise<{ locale: string }>;
}

export default async function RootLayout({
    children,
    params,
}: RootLayoutProps) {
    const { locale } = await params;
    if (!routing.locales.includes(locale as any)) {
        notFound();
    }
    const messages = await getMessages();

    return (
        <html lang={locale} suppressHydrationWarning>
            <head>
                <link
                    id="theme-link"
                    href={`/theme/theme-light/indigo/theme.css`}
                    rel="stylesheet"
                />
            </head>
            <body>
                <OlkProvider locale={locale} env={env}>
                    <EnvProvider env={env}>
                        <NextIntlClientProvider
                            locale={locale}
                            messages={messages}
                        >
                            <StoreProvider>
                                <PrimeReactProvider>
                                    <LayoutProvider>{children}</LayoutProvider>
                                </PrimeReactProvider>
                            </StoreProvider>
                        </NextIntlClientProvider>
                    </EnvProvider>
                </OlkProvider>
            </body>
        </html>
    );
}
