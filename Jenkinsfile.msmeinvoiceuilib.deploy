pipeline {
    agent any

    parameters {
        string(
            name: '<PERSON><PERSON>CH_TO_BUILD',
            defaultValue: 'develop',
            description: 'The Git branch to build'
        )
        string(
            name: 'SUCCESS_EMAIL',
            defaultValue: '<EMAIL>,<EMAIL>',
            description: 'Comma-separated email addresses for success notifications'
        )
        string(
            name: 'FAILURE_EMAIL',
            defaultValue: '<EMAIL>,<EMAIL>',
            description: 'Comma-separated email addresses for failure notifications'
        )
    }

    environment {
        GIT_URL = 'ssh://git-codecommit.ap-south-1.amazonaws.com/v1/repos/msme-invoice-ui-lib'
        SONARQUBE_SERVER = 'teapot-sonar'
        SONAR_PROJECT_KEY = 'msme-invoice-ui-lib'
        ECR_REPO_URI = '004335400582.dkr.ecr.ap-south-1.amazonaws.com/msme-platform-images'

        SAFE_BRANCH_NAME = "${params.BRANCH_TO_BUILD.replace('/', '-')}"
        IMAGE_TAG = "msme-invoiceui-lib-${SAFE_BRANCH_NAME.toLowerCase()}-${env.BUILD_NUMBER}"
    }

    stages {
        stage('Clone Repository') {
            steps {
                script {
                    git branch: "${params.BRANCH_TO_BUILD}", url: "${GIT_URL}"
                    env.GIT_COMMIT_ID = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
                }
            }
        }

        stage('Install Dependencies & Build olk') {
            steps {
                script {
                    sh '''
                        rm -rf node_modules
                        npm install

                        cd olk-package
                        npm install
                        npm run build
                        cd ..

                        mkdir -p node_modules/@iris-olk/invoice
                        cp -r olk-package/dist/* node_modules/@iris-olk/invoice/
                        cp olk-package/package.json node_modules/@iris-olk/invoice/
                    '''
                }
            }
        }

        stage('Build Project') {
            steps {
                script {
                    sh 'node --stack-size=8192 node_modules/.bin/next build'
                }
            }
        }

        stage('Build Docker Image') {
            steps {
                script {
                    sh "docker build -t ${IMAGE_TAG} ."
                }
            }
        }

        stage('Push Docker Image to ECR') {
            steps {
                script {
                    sh "aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${ECR_REPO_URI}"
                    sh "docker tag ${IMAGE_TAG} ${ECR_REPO_URI}:${IMAGE_TAG}"
                    sh "docker push ${ECR_REPO_URI}:${IMAGE_TAG}"
                }
            }
        }

        stage('Cleanup Docker Images') {
            when {
                expression { currentBuild.result == null }
            }
            steps {
                script {
                    echo 'Checking disk space usage before Docker cleanup...'

                    def usage = sh(script: "df -h / | awk 'NR==2 {print \$(NF-1)}' | sed 's/%//'", returnStdout: true).trim().toInteger()

                    if (usage >= 80) {
                        echo "Disk usage is ${usage}%. Proceeding with Docker cleanup."
                        sh '''
                            docker image prune -af
                            docker container prune -f
                            docker volume prune -f
                            docker system prune -af --volumes
                        '''
                    } else {
                        echo "Disk usage is ${usage}%. No cleanup needed."
                    }
                }
            }
        }

        stage('Tag Git Commit') {
            when {
                expression { currentBuild.result == null }
            }
            steps {
                script {
                    sh "git tag ${IMAGE_TAG}"
                    sh "git push origin ${IMAGE_TAG}"
                }
            }
        }
    }

    post {
        success {
            echo 'Build and Docker image push were successful!'

            emailext(
                subject: "\uD83C\uDF89 Build Successful: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                body: """
                    <html>
                        <body>
                            <h2>\uD83C\uDF89 Build Successful!</h2>
                            <p>The build and Docker image push to ECR were completed successfully.</p>
                            <h3>Details:</h3>
                            <ul>
                                <li><strong>Project:</strong> ${env.JOB_NAME}</li>
                                <li><strong>Build Number:</strong> ${env.BUILD_NUMBER}</li>
                                <li><strong>Branch:</strong> ${params.BRANCH_TO_BUILD}</li>
                                <li><strong>Docker Image:</strong> ${IMAGE_TAG}</li>
                                <li><strong>ECR Repository:</strong> ${ECR_REPO_URI}</li>
                            </ul>
                            <p>Visit the build log: <a href="${env.BUILD_URL}">${env.BUILD_URL}</a></p>
                        </body>
                    </html>
                """,
                mimeType: 'text/html',
                to: "${params.SUCCESS_EMAIL}"
            )
        }

        failure {
            emailext(
                subject: "\u274C Build Failed: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                body: """
                    <html>
                        <body>
                            <h2>\u274C Build Failed</h2>
                            <p>The build encountered an error.</p>
                            <h3>Details:</h3>
                            <ul>
                                <li><strong>Project:</strong> ${env.JOB_NAME}</li>
                                <li><strong>Build Number:</strong> ${env.BUILD_NUMBER}</li>
                                <li><strong>Branch:</strong> ${params.BRANCH_TO_BUILD}</li>
                            </ul>
                            <p>Check the build logs for more information: <a href="${env.BUILD_URL}">${env.BUILD_URL}</a></p>
                        </body>
                    </html>
                """,
                mimeType: 'text/html',
                to: "${params.FAILURE_EMAIL}",
                attachLog: true,
                compressLog: true
            )
        }
    }
}
