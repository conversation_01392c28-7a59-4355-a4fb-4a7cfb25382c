# # Use the latest Node.js image
# FROM node:latest

# # Set the working directory inside the container
# WORKDIR /docker/main

# # Copy the current directory contents into the container at /docker/main
# COPY . .

# # Install the necessary dependencies (like npm modules)
# RUN npm install

# # Expose port 3000 for the web UI (ensure the app runs on this port)
# EXPOSE 3000

# # Set the command to run the app (dev server for development)
# CMD ["npm", "run", "dev"]

FROM node:21.5.0
WORKDIR /app
COPY . .
RUN npm install

# Build olk-package
RUN cd olk-package && \
    npm install && \
    npm run build

# Copy built olk-package into main app's node_modules
RUN mkdir -p node_modules/@iris-olk/invoice && \
    cp -r olk-package/dist/* node_modules/@iris-olk/invoice/ && \
    cp olk-package/package.json node_modules/@iris-olk/invoice/

RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]