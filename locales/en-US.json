{"LoginForm": {"loginTitle": "<PERSON><PERSON>", "emailPlaceholder": "Email Address", "loginButton": "<PERSON><PERSON>", "loginFailed": "<PERSON><PERSON> failed"}, "RegistrationForm": {"registerTitle": "Register", "orgNamePlaceholder": "Organisation Name", "orgNameRequired": "Org Name is required", "orgNameEmpty": "Org Name cannot be empty", "panPlaceholder": "PAN", "invalidPanFormat": "Invalid PAN format (e.g., **********)", "gstinPlaceholder": "GSTIN", "invalidGstinFormat": "Invalid GSTIN format (e.g., 22**********1Z5)", "emailPlaceholder": "Email", "emailRequired": "Email is required", "emailEmpty": "Email cannot be empty", "invalidEmailFormat": "Invalid email format", "namePlaceholder": "Name", "mobilePlaceholder": "Mobile", "registerButton": "Register", "successSummary": "Success", "successDetail": "Registered successfully", "errorSummary": "Error", "errorDetail": "Registration failed"}, "OlkDashboard": {"addParty": "Add Party", "addProduct": "Add Product/Service", "addSales": "Add Sales", "addPurchase": "Add Purchase", "totalSales": "Total Sales", "totalPurchases": "Total Purchases", "receivables": "Receivables", "payables": "Payables"}, "SalesPurchaseChart": {"chartTitle": "Monthly Sales vs Purchases", "datasets": {"sales": "Total Sales", "purchases": "Total Purchases"}, "months": {"jan": "Jan", "feb": "Feb", "mar": "Mar", "apr": "Apr", "may": "May", "jun": "Jun", "jul": "Jul", "aug": "Aug", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Dec"}}, "InvoiceList": {"locale": "en-US", "invoiceTypes": {"sales": "Sales", "purchase": "Purchase"}, "headers": {"invoiceNo": "Invoice No.", "invoiceDate": "Invoice Date", "partyName": "Party Name", "totalAmount": "Total Amount", "amountPaid": "Amount <PERSON>", "action": "Action"}, "actions": {"view": "View"}, "messages": {"loading": "Loading...", "noInvoices": "No invoices found."}, "errors": {"invalidResponse": "Invalid response format", "fetchFailed": "Failed to fetch invoices. Please try again."}}, "InvoiceView": {"locale": "en-US", "currencySymbol": "₹", "invoiceTypes": {"sales": "Sales Invoice", "purchase": "Purchase Invoice", "unknown": "Unknown Invoice"}, "paymentModes": {"credit": "On Credit", "cash": "Cash Received", "bankTransfer": "Bank Transfer", "unknown": "Unknown"}, "sections": {"customerDetails": "Customer Details", "consigneeDetails": "Consignee <PERSON>", "products": "Products", "paymentDetails": "Payment Details", "invoiceSummary": "Invoice Summary", "transportation": "Mode of Transportation"}, "labels": {"invoiceNo": "Invoice No", "invoiceDate": "Invoice Date", "name": "Name", "phone": "Phone", "email": "Email", "address": "Address", "state": "State", "pincode": "Pincode", "consigneeName": "Consignee Name", "consigneeAddress": "Consignee Address", "consigneeState": "Consignee State", "consigneePincode": "Consignee <PERSON>", "paymentMode": "Payment Mode", "bankDetails": "Bank Details", "transportationMode": "Transportation Mode", "vehicleNo": "Vehicle No", "dateOfSupply": "Date of Supply", "totalTaxableAmount": "Total Taxable Amount", "totalDiscount": "Total Discount", "totalCGST": "Total CGST", "totalSGST": "Total SGST", "totalIGST": "Total IGST", "totalInvoiceAmount": "Total Invoice Amount", "totalPaidAmount": "Total Paid Amount", "totalInWords": "Total in Words", "narration": "Narration", "na": "N/A"}, "productHeaders": {"name": "Product Name", "quantity": "Quantity", "freeQty": "Free Qty", "pricePerUnit": "Price/Unit", "discount": "Discount", "taxableAmount": "Taxable Amt", "gstRate": "GST Rate", "gstAmount": "GST Amount", "totalAmount": "Total Amount"}, "errors": {"apiNotConfigured": "API base URL is not configured", "noInvoiceId": "No invoice ID provided", "noInvoiceRecord": "No invoice record found", "fetchFailed": "Failed to fetch invoice details", "invoiceNotFound": "Invoice not found for invid: {invoiceId}"}, "messages": {"noInvoiceData": "No invoice data available"}}, "ReceiverDetails": {"labels": {"receiverDetails": "Receiver Details", "billedTo": "Billed To", "selectParty": "Select Party", "gstin": "GSTIN", "address": "Address", "state": "State", "pincode": "Pincode", "na": "N/A", "sameAsBilling": "Same as <PERSON><PERSON> Address", "consigneeName": "Consignee Name", "consigneeAddress": "Consignee Address"}, "placeholders": {"selectParty": "Select a party", "selectState": "Select a state"}, "states": {"Jammu and Kashmir": "Jammu and Kashmir", "Himachal Pradesh": "Himachal Pradesh", "Punjab": "Punjab", "Chandigarh": "Chandigarh", "Uttarakhand": "Uttarakhand", "Haryana": "Haryana", "Delhi": "Delhi", "Rajasthan": "Rajasthan", "Uttar Pradesh": "Uttar Pradesh", "Bihar": "Bihar", "Sikkim": "Sikkim", "Arunachal Pradesh": "Arunachal Pradesh", "Nagaland": "Nagaland", "Manipur": "Manipur", "Mizoram": "Mizoram", "Tripura": "<PERSON>ura", "Meghalaya": "<PERSON><PERSON><PERSON>", "Assam": "Assam", "West Bengal": "West Bengal", "Jharkhand": "Jharkhand", "Odisha": "Odisha", "Chhattisgarh": "Chhattisgarh", "Madhya Pradesh": "Madhya Pradesh", "Gujarat": "Gujarat", "Daman and Diu": "<PERSON><PERSON> and <PERSON><PERSON>", "Dadra and Nagar Haveli": "Dadra and Nagar Haveli", "Maharashtra": "Maharashtra", "Andhra Pradesh": "Andhra Pradesh", "Karnataka": "Karnataka", "Goa": "Goa", "Lakshdweep": "Lakshdweep", "Kerala": "Kerala", "Tamil Nadu": "Tamil Nadu", "Pondicherry": "Pondicherry", "Andaman and Nicobar Islands": "Andaman and Nicobar Islands", "Telangana": "Telangana", "Andhra Pradesh (New)": "Andhra Pradesh (New)"}, "errors": {"error": "Error", "somethingWentWrong": "Something went wrong!", "failedToFetchParties": "Failed to fetch parties."}, "warnings": {"noData": "No Data", "noPartiesFound": "No parties found."}}, "PaymentOptions": {"methods": {"cash": "Cash", "bank": "Bank", "credit": "On Credit", "online": "Online"}, "labels": {"paymentMode": "Payment Mode", "cashReceived": "Cash Received", "bankName": "Bank Name", "accountNo": "Account No", "ifsc": "IFSC Code", "txnId": "Transaction ID"}, "placeholders": {"selectPaymentMode": "Select Payment Mode", "cashReceived": "Enter amount received", "bankName": "Enter bank name", "accountNo": "Enter account number", "ifsc": "Enter IFSC code", "txnId": "Enter transaction ID"}}, "ProductTable": {"currencySymbol": "₹", "labels": {"product": "Product/Service", "hsn": "HSN", "quantity": "Qty", "freeQuantity": "Free Qty", "amount": "Amount", "discount": "Discount", "taxableValue": "Taxable Value", "gstRate": "GST Rate", "gstAmount": "GST Amount", "total": "Total"}, "headers": {"product": "Product/Service", "hsn": "HSN", "quantity": "Qty", "freeQuantity": "Free Qty", "amount": "Amount", "discount": "Discount", "taxableValue": "Taxable Value", "gst": "GST", "gstAmount": "GST Amount", "total": "Total", "action": "Action"}, "placeholders": {"selectProduct": "Select Product", "quantity": "0.00", "freeQuantity": "0.00", "amount": "0.00", "discount": "0.00"}, "buttons": {"addProduct": "Add Product"}, "discountTypes": {"amount": "Amt", "percentage": "%"}, "errors": {"error": "Error", "somethingWentWrong": "Something went wrong", "apiError": "API Error", "failedToFetchProducts": "Failed to fetch products"}, "messages": {"noData": "No Data", "noProductsFound": "No products found"}}, "TransportMode": {"labels": {"modeOfTransport": "Mode of Transport"}, "placeholders": {"selectMode": "Select Mode"}, "modes": {"none": "None", "road": "By Road", "rail": "By Rail", "air": "By Air", "ship": "By Ship"}}, "AddPartyForm": {"dialog": {"editParty": "Edit Party"}, "sections": {"role": "Party Role", "basicDetails": "Basic Details", "address": "Address", "taxInfo": "Tax Information", "bankDetails": "Bank Details"}, "roles": {"customer": "Customer", "supplier": "Supplier"}, "labels": {"name": "Name", "email": "Email", "contact": "Contact", "address": "Address", "state": "State", "pincode": "Pincode", "pan": "PAN", "gstin": "GSTIN", "accName": "Account Name", "accNo": "Account Number", "branch": "Branch", "ifsc": "IFSC"}, "placeholders": {"selectState": "Select a state"}, "buttons": {"reset": "Reset", "save": "Save", "saveParty": "Save Party"}, "validation": {"summary": "Validation", "selectValidState": "Please select a valid state.", "nameRequired": "Please enter the name.", "nameMaxLength": "Name cannot exceed 100 characters", "emailMinLength": "Email must be at least 6 characters", "emailMaxLength": "Email cannot exceed 100 characters", "invalidEmailFormat": "Invalid email format", "contactRequired": "Contact is required", "contactMinLength": "Contact must be at least 6 digits", "contactMaxLength": "Contact cannot exceed 12 digits", "contactNumbersOnly": "Contact must contain only numbers", "addressRequired": "Please enter the address", "pincodeRequired": "Please enter the pincode.", "invalidPanFormat": "Invalid PAN format (e.g., **********)", "invalidGstinFormat": "Invalid GSTIN format (e.g., 27**********1Z5)"}, "success": {"summary": "Success", "partyAdded": "Party added successfully.", "partyEdited": "Party edited successfully"}, "errors": {"summary": "Error", "failedToSave": "Failed to save party.", "failedToUpdate": "Failed to update party."}}, "PartyDetailsDialog": {"header": "Party Details", "partyTypes": {"customer": "Customer", "supplier": "Supplier"}, "sections": {"bankDetails": "Bank Details"}, "labels": {"contact": "Contact", "email": "Email", "address": "Address", "state": "State", "pincode": "Pincode", "gstin": "GSTIN", "pan": "PAN", "accName": "Account Name", "accNo": "Account No", "ifsc": "IFSC", "branch": "Branch", "na": "N/A", "dash": "-"}}, "PartyListTable": {"filters": {"show": "Show", "both": "Both", "customer": "Customer", "supplier": "Supplier"}, "headers": {"name": "Name", "contact": "Contact", "type": "Type", "state": "State", "gstin": "GSTIN", "actions": "Actions"}, "partyTypes": {"customer": "Customer", "supplier": "Supplier", "unknown": "Unknown"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete"}, "errors": {"error": "Error", "fetchFailed": "Failed to fetch party list", "apiError": "API error occurred"}, "messages": {"info": "Info", "noParties": "No party is added", "partyInfo": "Party Info", "viewingParty": "Viewing {name}"}}, "AddPSForm": {"itemType": "Item Type", "product": "Product", "service": "Service", "name": "Name", "nameRequired": "Name is required", "nameMaxLength": "Name cannot exceed 100 characters", "uom": "Unit of Measurement", "uomRequired": "Unit of Measurement is required", "selectUOM": "Select UOM", "hsn": "HSN", "hsnValidation": "HSN must be 4, 6, or 8 digits and not all zeroes", "hsnValid": "Please enter the valid HSN code.", "sacValid": "'Please enter the valid SAC code.", "gst": "GST %", "selectGST": "Select GST rate", "mrp": "MRP", "mrpValidation": "Up to 12 digits with 2 decimals allowed", "salePrice": "Sale Price", "priceValidation": "Up to 12 digits with 2 decimals allowed", "discountAmount": "Discount Amount", "discountPercent": "Discount %", "reset": "Reset", "save": "Save", "saving": "Saving...", "editProduct": "Edit Product", "success": "Success", "error": "Error", "updateSuccess": "Product updated successfully", "addSuccess": "Product added successfully", "updateError": "Failed to update product", "addError": "Failed to add product"}, "ProdListTable": {"orgNotFound": "Organization not found.", "productsNotFound": "Product/service not found or available", "noProducts": "No product and service added", "fetchError": "Something went wrong while fetching products.", "show": "Show", "both": "Both", "product": "Product", "service": "Service", "name": "Name", "type": "Type", "hsn": "HSN", "gst": "GST", "mrp": "MRP", "salePrice": "Sale Price", "actions": "Actions", "view": "View", "edit": "Edit", "delete": "Delete", "deleteConfirm": "Are you sure you want to delete {name}?", "productDetails": "Product Details", "uom": "UOM", "discountAmount": "Discount Amount", "discountPercent": "Discount %"}}