{"LoginForm": {"loginTitle": "लॉग इन", "emailPlaceholder": "ईमेल पता", "loginButton": "लॉग इन करें", "loginFailed": "लॉगिन विफल हुआ"}, "RegistrationForm": {"registerTitle": "पं<PERSON><PERSON><PERSON><PERSON><PERSON>", "orgNamePlaceholder": "संगठन का नाम", "orgNameRequired": "संगठन का नाम आवश्यक है", "orgNameEmpty": "संगठन का नाम खाली नहीं हो सकता", "panPlaceholder": "पैन", "invalidPanFormat": "अमान्य पैन प्रारूप (जैसे, **********)", "gstinPlaceholder": "जीएसटीआईएन", "invalidGstinFormat": "अमान्य जीएसटीआईएन प्रारूप (जैसे, 22**********1Z5)", "emailPlaceholder": "ईमेल", "emailRequired": "ईमेल आवश्यक है", "emailEmpty": "ईमेल खाली नहीं हो सकता", "invalidEmailFormat": "अमान्य ईमेल प्रारूप", "namePlaceholder": "नाम", "mobilePlaceholder": "मोबाइल", "registerButton": "पंजी<PERSON><PERSON><PERSON> करें", "successSummary": "सफलता", "successDetail": "सफलतापूर्वक पंजीकृत", "errorSummary": "त्रुटि", "errorDetail": "पंजीकरण विफल"}, "OlkDashboard": {"addParty": "पार्टी जोड़ें", "addProduct": "उत्पाद/सेवा जोड़ें", "addSales": "बिक्री जोड़ें", "addPurchase": "खरीद जोड़ें", "totalSales": "कुल बिक्री", "totalPurchases": "कुल खरीद", "receivables": "प्राप्य", "payables": "देय"}, "SalesPurchaseChart": {"chartTitle": "मासिक बिक्री बनाम खरीद", "datasets": {"sales": "कुल बिक्री", "purchases": "कुल खरीद"}, "months": {"jan": "जन", "feb": "फर", "mar": "मार<PERSON><PERSON>", "apr": "अप्रैल", "may": "मई", "jun": "जून", "jul": "जुलाई", "aug": "अगस्त", "sep": "सितं", "oct": "अक्टू", "nov": "नवं", "dec": "दिसं"}}, "InvoiceList": {"locale": "hi-IN", "invoiceTypes": {"sales": "बिक<PERSON>री", "purchase": "खरीद"}, "headers": {"invoiceNo": "चालान संख्या", "invoiceDate": "चालान तिथि", "partyName": "पार्टी का नाम", "totalAmount": "क<PERSON>ल राशि", "amountPaid": "भुगतान राशि", "action": "कार्रवाई"}, "actions": {"view": "देखें"}, "messages": {"loading": "लोड हो रहा है...", "noInvoices": "कोई चालान नहीं मिला।"}, "errors": {"invalidResponse": "अमान्य प्रतिक्रिया प्रारूप", "fetchFailed": "चालान प्राप्त करने में विफल। कृपया पुनः प्रयास करें।"}}, "InvoiceView": {"locale": "hi-IN", "currencySymbol": "₹", "invoiceTypes": {"sales": "बिक्री चालान", "purchase": "खरीद चालान", "unknown": "अज्ञात चालान"}, "paymentModes": {"credit": "क्रेडिट पर", "cash": "नकद प्राप्त", "bankTransfer": "बैंक ट्रांसफर", "unknown": "अज्ञात"}, "sections": {"customerDetails": "ग्राहक विवरण", "consigneeDetails": "प्राप्तकर्ता विवरण", "products": "उत्पाद", "paymentDetails": "भुगतान विवरण", "invoiceSummary": "चालान सारांश", "transportation": "परिवहन का तरीका"}, "labels": {"invoiceNo": "चालान संख्या", "invoiceDate": "चालान तिथि", "name": "नाम", "phone": "फोन", "email": "ईमेल", "address": "पता", "state": "राज<PERSON>य", "pincode": "पिनकोड", "consigneeName": "प्राप्तकर्ता का नाम", "consigneeAddress": "प्राप्तकर्ता का पता", "consigneeState": "प्राप्तकर्ता का राज्य", "consigneePincode": "प्राप्तकर्ता का पिनकोड", "paymentMode": "भुगतान का तरीका", "bankDetails": "बैंक विवरण", "transportationMode": "परिवहन का तरीका", "vehicleNo": "वाहन संख्या", "dateOfSupply": "आपूर्ति की तिथि", "totalTaxableAmount": "कुल कर योग्य राशि", "totalDiscount": "कुल छूट", "totalCGST": "कुल CGST", "totalSGST": "कुल SGST", "totalIGST": "कुल IGST", "totalInvoiceAmount": "क<PERSON>ल चालान राशि", "totalPaidAmount": "कुल भुगतान राशि", "totalInWords": "कुल राशि शब्दों में", "narration": "विवरण", "na": "उपलब्ध नहीं"}, "productHeaders": {"name": "उत्पाद का नाम", "quantity": "मात्रा", "freeQty": "मुफ्त मात्रा", "pricePerUnit": "प्रति इकाई मूल्य", "discount": "छूट", "taxableAmount": "कर योग्य राशि", "gstRate": "G<PERSON> दर", "gstAmount": "GST राशि", "totalAmount": "क<PERSON>ल राशि"}, "errors": {"apiNotConfigured": "API बेस URL कॉन्फ़िगर नहीं है", "noInvoiceId": "कोई चालान ID प्रदान नहीं किया गया", "noInvoiceRecord": "कोई चालान रिकॉर्ड नहीं मिला", "fetchFailed": "चालान विवरण प्राप्त करने में विफल", "invoiceNotFound": "इनविड के लिए चालान नहीं मिला: {invoiceId}"}, "messages": {"noInvoiceData": "कोई चालान डेटा उपलब्ध नहीं है"}}, "ReceiverDetails": {"labels": {"receiverDetails": "प्राप्तकर्ता विवरण", "billedTo": "बिल किया गया", "selectParty": "पार्टी चुनें", "gstin": "जीएसटीआईएन", "address": "पता", "state": "राज<PERSON>य", "pincode": "पिनकोड", "na": "उपलब्ध नहीं", "sameAsBilling": "बिलिंग पते के समान", "consigneeName": "कनसाइनी का नाम", "consigneeAddress": "कनसाइनी का पता"}, "placeholders": {"selectParty": "पार्टी चुनें", "selectState": "राज्य चुनें"}, "states": {"Jammu and Kashmir": "जम्मू और कश्मीर", "Himachal Pradesh": "हिमा<PERSON><PERSON> प्रदेश", "Punjab": "पं<PERSON><PERSON><PERSON>", "Chandigarh": "चंडीगढ़", "Uttarakhand": "उत्तराखंड", "Haryana": "हरियाणा", "Delhi": "दिल्ली", "Rajasthan": "राजस्थान", "Uttar Pradesh": "उत्तर प्रदेश", "Bihar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sikkim": "सिक्किम", "Arunachal Pradesh": "अरुणाचल प्रदेश", "Nagaland": "नागालैंड", "Manipur": "मण<PERSON><PERSON><PERSON>र", "Mizoram": "मिज़ोरम", "Tripura": "त्रिपुरा", "Meghalaya": "मेघालय", "Assam": "असम", "West Bengal": "पश्चिम बंगाल", "Jharkhand": "झारखंड", "Odisha": "ओड़िशा", "Chhattisgarh": "छत्तीसगढ़", "Madhya Pradesh": "मध्य प्रदेश", "Gujarat": "गुजरात", "Daman and Diu": "दमन और दीव", "Dadra and Nagar Haveli": "दादरा और नगर हवेली", "Maharashtra": "महाराष्ट्र", "Andhra Pradesh": "आंध्र प्रदेश", "Karnataka": "कर्नाटक", "Goa": "गोवा", "Lakshdweep": "लक्षद्वीप", "Kerala": "केरल", "Tamil Nadu": "तमिलनाडु", "Pondicherry": "पांडिचेरी", "Andaman and Nicobar Islands": "अंडमान और निकोबार द्वीप समूह", "Telangana": "तेलंगाना", "Andhra Pradesh (New)": "आंध्र प्रदेश (नया)"}, "errors": {"error": "त्रुटि", "somethingWentWrong": "कुछ गलत हो गया!", "failedToFetchParties": "पार्टियाँ लाने में विफल।"}, "warnings": {"noData": "कोई डेटा नहीं", "noPartiesFound": "कोई पार्टी नहीं मिली।"}}, "PaymentOptions": {"methods": {"cash": "नकद", "bank": "बैंक", "credit": "क्रेडिट पर", "online": "ऑनलाइन"}, "labels": {"paymentMode": "भुगतान का तरीका", "cashReceived": "प्राप्त नकद राशि", "bankName": "बैंक का नाम", "accountNo": "खाता संख्या", "ifsc": "आईएफएससी कोड", "txnId": "लेन-देन आईडी"}, "placeholders": {"selectPaymentMode": "भुगतान का तरीका चुनें", "cashReceived": "प्राप्त राशि दर्ज करें", "bankName": "बैंक का नाम दर्ज करें", "accountNo": "खाता संख्या दर्ज करें", "ifsc": "आईएफएससी कोड दर्ज करें", "txnId": "लेन-देन आईडी दर्ज करें"}}, "ProductTable": {"currencySymbol": "₹", "labels": {"product": "उत्पाद/सेवा", "hsn": "HSN", "quantity": "मात्रा", "freeQuantity": "मुफ्त मात्रा", "amount": "रा<PERSON>ि", "discount": "छूट", "taxableValue": "कर योग्य राशि", "gstRate": "G<PERSON> दर", "gstAmount": "GST राशि", "total": "कुल"}, "headers": {"product": "उत्पाद/सेवा", "hsn": "HSN", "quantity": "मात्रा", "freeQuantity": "मुफ्त मात्रा", "amount": "रा<PERSON>ि", "discount": "छूट", "taxableValue": "कर योग्य राशि", "gst": "GST", "gstAmount": "GST राशि", "total": "कुल", "action": "कार्रवाई"}, "placeholders": {"selectProduct": "उत्पाद चुनें", "quantity": "0.00", "freeQuantity": "0.00", "amount": "0.00", "discount": "0.00"}, "buttons": {"addProduct": "उत्पाद जोड़ें"}, "discountTypes": {"amount": "रा<PERSON>ि", "percentage": "%"}, "errors": {"error": "त्रुटि", "somethingWentWrong": "कुछ गलत हुआ", "apiError": "API त्रुटि", "failedToFetchProducts": "उत्पाद प्राप्त करने में विफल"}, "messages": {"noData": "कोई डेटा नहीं", "noProductsFound": "कोई उत्पाद नहीं मिला"}}, "TransportMode": {"labels": {"modeOfTransport": "परिवहन का तरीका"}, "placeholders": {"selectMode": "तरीका चुनें"}, "modes": {"none": "कोई नहीं", "road": "सड़क द्वारा", "rail": "रेल द्वारा", "air": "हवाई मार्ग द्वारा", "ship": "<PERSON><PERSON><PERSON><PERSON> द्वारा"}}, "AddPartyForm": {"dialog": {"editParty": "पार्टी संपादित करें"}, "sections": {"role": "पार्टी की भूमिका", "basicDetails": "मूल विवरण", "address": "पता", "taxInfo": "टैक्स जानकारी", "bankDetails": "बैंक विवरण"}, "roles": {"customer": "ग्राहक", "supplier": "आपूर्तिकर्ता"}, "labels": {"name": "नाम", "email": "ईमेल", "contact": "संपर्क", "address": "पता", "state": "राज<PERSON>य", "pincode": "पिन कोड", "pan": "पैन", "gstin": "जीएसटीआईएन", "accName": "खाता नाम", "accNo": "खाता संख्या", "branch": "शाखा", "ifsc": "आईएफएससी"}, "placeholders": {"selectState": "राज्य चुनें"}, "buttons": {"reset": "रीसेट", "save": "सहेजें", "saveParty": "पार्टी सहेजें"}, "validation": {"summary": "सत्यापन", "selectValidState": "कृपया एक वैध राज्य चुनें।", "nameRequired": "कृपया नाम दर्ज करें।", "nameMaxLength": "नाम 100 वर्णों से अधिक नहीं हो सकता", "emailMinLength": "ईमेल कम से कम 6 वर्णों का होना चाहिए", "emailMaxLength": "ईमेल 100 वर्णों से अधिक नहीं हो सकता", "invalidEmailFormat": "अमान्य ईमेल प्रारूप", "contactRequired": "संपर्क आवश्यक है", "contactMinLength": "संपर्क कम से कम 6 अंकों का होना चाहिए", "contactMaxLength": "संपर्क 12 अंकों से अधिक नहीं हो सकता", "contactNumbersOnly": "संपर्क में केवल संख्याएं होनी चाहिए", "addressRequired": "कृपया पता दर्ज करें", "pincodeRequired": "कृपया पिन कोड दर्ज करें।", "invalidPanFormat": "अमान्य पैन प्रारूप (जैसे, **********)", "invalidGstinFormat": "अमान्य जीएसटीआईएन प्रारूप (जैसे, 27**********1Z5)"}, "success": {"summary": "सफलता", "partyAdded": "पार्टी सफलतापूर्वक जोड़ी गई।", "partyEdited": "पार्टी सफलतापूर्वक संपादित की गई"}, "errors": {"summary": "त्रुटि", "failedToSave": "पार्टी सहेजने में विफल।", "failedToUpdate": "पार्टी अपडेट करने में विफल।"}}, "PartyDetailsDialog": {"header": "पार्टी विवरण", "partyTypes": {"customer": "ग्राहक", "supplier": "आपूर्तिकर्ता", "unknown": "अज्ञात"}, "sections": {"bankDetails": "बैंक विवरण"}, "labels": {"contact": "संपर्क", "email": "ईमेल", "address": "पता", "state": "राज<PERSON>य", "pincode": "पिन कोड", "gstin": "GSTIN", "pan": "PAN", "accName": "खाता नाम", "accNo": "खाता संख्या", "ifsc": "IFSC", "branch": "शाखा", "na": "उपलब्ध नहीं", "dash": "-"}}, "PartyListTable": {"filters": {"show": "दिखाएं", "both": "दोनों", "customer": "ग्राहक", "supplier": "आपूर्तिकर्ता"}, "headers": {"name": "नाम", "contact": "संपर्क", "type": "प्र<PERSON><PERSON>र", "state": "राज<PERSON>य", "gstin": "GSTIN", "actions": "कार्रवाई"}, "partyTypes": {"customer": "ग्राहक", "supplier": "आपूर्तिकर्ता", "unknown": "अज्ञात"}, "actions": {"view": "देखें", "edit": "संपादित करें", "delete": "हटाएं"}, "errors": {"error": "त्रुटि", "fetchFailed": "पार्टी सूची प्राप्त करने में विफल", "apiError": "API त्रुटि हुई"}, "messages": {"info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ी", "noParties": "कोई पार्टी नहीं जोड़ी गई", "partyInfo": "पार्टी जानकारी", "viewingParty": "{name} देख रहे हैं"}}, "AddPSForm": {"itemType": "आइटम प्रकार", "product": "उत्पाद", "service": "सेवा", "name": "नाम", "nameRequired": "नाम आवश्यक है", "nameMaxLength": "नाम 100 वर्णों से अधिक नहीं हो सकता", "uom": "माप की इकाई", "uomRequired": "माप की इकाई आवश्यक है", "selectUOM": "माप इकाई चुनें", "hsn": "HSN", "hsnValidation": "HSN 4, 6, या 8 अंकों का होना चाहिए और सभी शून्य नहीं होने चाहिए", "hsnValid": "कृपया वैध HSN कोड दर्ज करें।", "sacValid": "'कृपया वैध SAC कोड दर्ज करें।", "gst": "जीएसटी %", "selectGST": "जीएसटी दर चुनें", "mrp": "एमआरपी", "mrpValidation": "अधिकतम 12 अंक और 2 दशमलव स्थान तक की अनुमति है", "salePrice": "बिक्री मूल्य", "priceValidation": "अधिकतम 12 अंक और 2 दशमलव स्थान तक की अनुमति है", "discountAmount": "छूट राशि", "discountPercent": "छूट %", "reset": "रीसेट", "save": "सहेजें", "saving": "सहेजा जा रहा है...", "editProduct": "उत्पाद संपादित करें", "success": "सफलता", "error": "त्रुटि", "updateSuccess": "उत्पाद सफलतापूर्वक अद्यतन किया गया", "addSuccess": "उत्पाद सफलतापूर्वक जोड़ा गया", "updateError": "उत्पाद अद्यतन करने में विफल", "addError": "उत्पाद जोड़ने में विफल"}, "ProdListTable": {"orgNotFound": "संगठन नहीं मिला।", "productsNotFound": "उत्पाद/सेवा उपलब्ध नहीं है", "noProducts": "कोई उत्पाद और सेवा नहीं जोड़ी गई", "fetchError": "उत्पादों को प्राप्त करते समय कुछ गलत हुआ।", "show": "दिखाएं", "both": "दोनों", "product": "उत्पाद", "service": "सेवा", "name": "नाम", "type": "प्र<PERSON><PERSON>र", "hsn": "HSN", "gst": "GST", "mrp": "MRP", "salePrice": "बिक्री मूल्य", "actions": "कार्रवाई", "view": "देखें", "edit": "संपादित करें", "delete": "हटाएं", "deleteConfirm": "क्या आप वाकई {name} को हटाना चाहते हैं?", "productDetails": "उत्पाद विवरण", "uom": "माप इकाई", "discountAmount": "छूट राशि", "discountPercent": "छूट %"}}