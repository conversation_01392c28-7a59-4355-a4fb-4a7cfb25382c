import type { MenuModel } from "@/types";
import AppSubMenu from "./AppSubMenu";
import { useTranslations } from "next-intl";
import { getOrgDataField } from "@iris-olk/invoice";

const AppMenu = () => {
    const t = useTranslations("layouts.sidebar");
    const orgcode = Number(getOrgDataField("orgcode"));

    // Define invoiceItem based on orgcode
    const invoiceItem: MenuModel = orgcode
        ? {
              label: "Invoice",
              icon: "pi pi-file-edit",
              items: [
                  {
                      label: "Dashboard",
                      icon: "pi pi-sitemap",
                      to: "/dashboard/invoice",
                  },
                  {
                      label: "Add Party",
                      icon: "pi pi-user-plus",
                      to: "/dashboard/invoice/party",
                  },
                  {
                      label: "Add Product/Service",
                      icon: "pi pi-cart-plus",
                      to: "/dashboard/invoice/product",
                  },
                  {
                      label: "Add Sales",
                      icon: "pi pi-file-export",
                      to: "/dashboard/invoice/sales-and-purchase?type=sales",
                  },
                  {
                      label: "Add Purchase",
                      icon: "pi pi-file-import",
                      to: "/dashboard/invoice/sales-and-purchase?type=purchase",
                  },
                  {
                      label: "Cash Bill",
                      icon: "pi pi-money-bill",
                      to: "/dashboard/invoice/cashbill",
                  },

                  {
                      label: "Invoice Preferences",
                      icon: "pi pi-cog",
                      to: "/dashboard/invoice/preferences",
                  },
              ],
          }
        : {
              label: "Invoice",
              icon: "pi pi-file-edit",
              to: "/dashboard/invoice",
          };

    // Define the model array, including invoiceItem
    const model: MenuModel[] = [
        {
            label: t("dashboard.heading"),
            icon: "pi pi-home",
            items: [
                {
                    label: t("dashboard.home"),
                    icon: "pi pi-fw pi-home",
                    to: "/dashboard/home",
                },
                invoiceItem, // Add invoiceItem here
            ],
        },
    ];

    return <AppSubMenu model={model} />;
};

export default AppMenu;
