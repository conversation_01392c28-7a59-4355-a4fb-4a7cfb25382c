import { Sidebar } from "primereact/sidebar";
import { useContext, useEffect, useState } from "react";
import { LayoutContext } from "./context/layoutcontext";
import { useTranslations } from "next-intl";
import SignOutButton from "@/components/auth/SignOutButton";

const AppProfileSidebar = () => {
    const t = useTranslations("layouts.profileSidebar");
    const { layoutState, setLayoutState } = useContext(LayoutContext);
    const onProfileSidebarHide = () => {
        setLayoutState((prevState) => ({
            ...prevState,
            profileSidebarVisible: false,
        }));
    };

    return (
        <Sidebar
            visible={layoutState.profileSidebarVisible}
            onHide={onProfileSidebarHide}
            position="right"
            className="layout-profile-sidebar w-full sm:w-25rem"
        >
            <div className="flex flex-column mx-auto md:mx-0">
                <span className="mb-2 font-semibold">{t("welcome")}</span>
                <ul className="list-none m-0 p-0">
                    <li>
                        <a className="cursor-pointer flex surface-border mb-3 p-3 align-items-center border-1 surface-border border-round hover:surface-hover transition-colors transition-duration-150">
                            <span>
                                <i className="pi pi-power-off text-xl text-primary"></i>
                            </span>
                            <SignOutButton />
                        </a>
                    </li>
                </ul>
            </div>
        </Sidebar>
    );
};

export default AppProfileSidebar;
