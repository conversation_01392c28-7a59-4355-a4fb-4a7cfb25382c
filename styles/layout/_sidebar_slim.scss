@media screen and (min-width: $breakpoint) {
    .layout-container {
        &.layout-slim {
            .layout-topbar {
                .topbar-menubutton {
                    display: none;
                }
            }

            .sidebar-header {
                .app-logo {
                    .app-logo-normal {
                        display: none;
                    }

                    .app-logo-small {
                        display: inline;
                    }
                }
            }

            .layout-sidebar {
                width: 5rem;
                overflow: visible;
                z-index: 999;

                .layout-menu-container {
                    overflow: auto;

                    &::-webkit-scrollbar {
                        display: none;
                    }
                }
            }

            .layout-content-wrapper {
                margin-left: 5rem;
            }

            .layout-menu {
                ul {
                    display: none;
                }

                li.active-menuitem {
                    > ul {
                        display: block;
                    }
                }
                .layout-root-menuitem {
                    > .layout-menuitem-root-text {
                        display: none;
                    }

                    > a {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                        cursor: pointer;
                        outline: none;
                        transition: background-color var(--transition-duration);
                        width: 3rem;
                        height: 3rem;
                        margin: 0 auto 1rem auto;

                        .layout-submenu-toggler {
                            display: none;
                        }

                        .layout-menuitem-icon {
                            font-size: 1.5rem;
                            color: var(--menuitem-icon-color);
                        }

                        .layout-menuitem-text {
                            display: none;
                        }

                        &:hover {
                            background-color: var(--menuitem-hover-bg-color);
                        }
                    }

                    > ul {
                        display: none;
                        position: absolute;
                        left: 5rem;
                        top: 0;
                        min-width: 15rem;
                        background-color: var(--surface-overlay);
                        border-radius: var(--border-radius);
                        box-shadow: var(--sidebar-shadow);
                        border: var(--sidebar-border);
                        padding: 1rem;
                        max-height: 20rem;
                        overflow: auto;
                        z-index: 999;

                        a {
                            padding-right: 0.5rem;
                            color: var(--popup-submenu-item-text-color);
                            border-radius: var(--border-radius);

                            .layout-menuitem-icon {
                                color: var(--popup-submenu-item-icon-color);
                            }

                            &:hover {
                                background-color: var(--popup-submenu-item-hover-bg-color);
                            }
                        }

                        li {
                            a {
                                padding-left: 0.5rem;
                            }

                            li {
                                a {
                                    padding-left: 1rem;
                                }

                                li {
                                    a {
                                        padding-left: 1.5rem;
                                    }

                                    li {
                                        a {
                                            padding-left: 2rem;
                                        }

                                        li {
                                            a {
                                                padding-left: 2.5rem;
                                            }

                                            li {
                                                a {
                                                    padding-left: 3rem;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    &.active-menuitem {
                        > ul {
                            display: block;
                        }
                    }
                }
            }
        }
    }
}
