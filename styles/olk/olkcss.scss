.olk-button {
  font-size: 1.3rem; // slightly larger for desktop
  font-weight: 1000;
  color: white;
  border: 2px solid #c0c4c9; // soft gray border
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1); // subtle elevation
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}
.edit-modal{
  width: 50vw;
}
.disabled-cursor{
  cursor: not-allowed;
}


// Mobile-specific overrides
@media (max-width: 768px) {
  .olk-button {
    font-size: 15px !important; // Smaller text
    justify-content: flex-start !important; // Align content to the left
    text-align: center;
    width: 100%; // Make full width for better usability
  }

  // Hide PrimeReact button icons on mobile
  .olk-button .p-button-icon {
    display: none !important;
  }
  .edit-modal{
    width: 90vw;
  }
}



/* Grid layout */
.olk-card-grid {
    display: grid;
    display: inline-block;
    grid-template-columns: repeat(2, 1fr); // mobile: 2 cards per row
    gap: 1rem;
    margin-bottom: 1.5rem;
  
    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr); // desktop: 4 cards in one row
    }
  }
  
  /* Card container */
  .olk-card {
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    border-radius: 0.5rem;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
    padding: 0.75rem;
  }
  
  /* Inner content container */
  .olk-card-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  /* Title styling */
  .olk-card-title {
    font-size: 1.4rem;
    font-weight: 600; // bold for desktop
  
    @media (max-width: 768px) {
      font-size: 1.2rem;
      font-weight: 400; // no bold for mobile
      white-space: normal;
      word-break: break-word;
    }
  }
  
  /* Value styling (highlighted) */
  .olk-card-value {
    font-size: 1.3rem;
    font-weight: 700;
    margin-top: 0.3rem;
  }
  
  .text-green { color: #15803d; }   // Tailwind green-700
  .text-blue  { color: #1d4ed8; }   // Tailwind blue-700
  .text-orange { color: #c2410c; }  // Tailwind orange-700
  .text-red   { color: #b91c1c; }   // Tailwind red-700
  

  .scroll-wrapper {
    overflow-x: auto;
  }
  
  .data-table {
    min-width: 600px;
  }
  
  .form-label {
    display: block;
    margin-bottom: 0.5rem; /* same as mb-2 */
    font-weight: 500; /* same as font-medium */
    font-size: 1rem; /* optional: make it consistent */
    color: #374151; 
  }
  
  .form-footer {
    border-top: 2px solid #e5e7eb; /* Light gray border */
    background-color: #f9fafb; /* Very light gray background */
    padding: 0.50rem; /* 8px padding */
    display: flex;
    justify-content: flex-start;
    gap: 1rem; /* 16px gap between buttons */
    margin-top: 0.5rem; /* 8px margin from form */
  }
  
  .form-footer .p-button {
    display: block; /* Ensures each button can take full width */
    text-align: left; /* Left-aligns icon and label */
    width: fit-content; /* Button width matches content (text + icon) */
    padding: 0.25rem 0.5rem; /* Reduced padding to minimize extra space */
    font-size: 1rem; /* Ensure text size is consistent */
    line-height: 1.9; /* Prevent extra vertical space */
  }
  
  .p-button .p-button-icon {
    font-size: 1.1rem; /* Match icon size to text */
    margin-right: 0.5rem; /* Space between icon and text */
  }
  
  .p-button .p-button-label {
    font-size: 1.1rem; /* Match label size to text */
  }
  
  .p-button-secondary {
    background-color: #6c757d; /* Gray color */
    border-color: #6c757d;
    color: white;
  }
  
  .p-button-success {
    background-color: #28a745; /* Green color */
    border-color: #28a745;
    color: white;
  }  
/* General style for all tab headers */
.p-tabview .p-tabview-nav-link {
  font-size: 16px;
  font-weight: 500;
  color: #4b5563; 
  transition: all 0.3s ease;
}

/* Style for the ACTIVE tab header */
.p-tabview .p-tabview-nav .p-tabview-nav-link.p-tabview-nav-link-active { 
  font-size: 18px;
  font-weight: 800;
  color: #1d4ed8; /* your blue */
  border-bottom: 30px solid #1d4ed8;
}

.p-tabview .p-tabview-nav-link:hover {
  color: #1d4ed8;
}

.invoice-summary-container {
  display: flex !important;
  flex-direction: column !important;
  gap: 0.5rem !important;
  font-family: var(--font-family);
  color: var(--text-color);
}

/* Ensure each row doesn’t wrap or collapse */
.invoice-summary-row {
  display: flex !important;
  flex-direction: row !important;
  justify-content: space-between !important;
  align-items: center !important;
  flex-wrap: nowrap !important;
  width: 100% !important;
}

.invoice-summary-container .font-bold {
  color: var(--primary-color);
}

/* Override PrimeReact formgrid to ensure Tailwind grid behavior */
.card-grid-container {
  display: grid !important;
  grid-template-columns: repeat(1, 1fr) !important; /* 1 column on mobile */
}

@media (min-width: 768px) {
  .card-grid-container {
    grid-template-columns: repeat(3, 1fr) !important; /* 3 columns on medium screens and up */
  }
}

/* Ensure cards don’t wrap unnecessarily */
.card-grid-container > .p-card {
  width: 100% !important;
  box-sizing: border-box !important;
}

/* In olkcss.scss */
.p-datatable .p-datatable-header {
  display: flex;
  align-items: center;
}

// styles/olk/olkcss.scss
.invoice-container,
.invoice-list-container,
.invoice-view-container {
  padding: 2rem;
  background-color: #f8f9fa;

  h1 {
    margin-bottom: 1.5rem;
    color: #333;
  }
}

.invoice-table {
  .p-datatable-table {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .p-datatable-header {
    background-color: #f1f3f5;
  }
}

.invoice-view-container {
  .p-card {
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

    .p-card-title {
      font-size: 1.5rem;
      color: #2c3e50;
    }

    .p-grid {
      margin-top: 1rem;

      .p-col-6 {
        padding: 1rem;

        h3 {
          font-size: 1.2rem;
          margin-bottom: 0.5rem;
          color: #34495e;
        }

        p {
          margin: 0.3rem 0;
          color: #555;
        }
      }
    }
  }
}

.p-field-radiobutton {
  margin-bottom: 1rem;

  label {
    margin-left: 0.5rem;
    color: #333;
  }
}

.custom-button {
  &.custom-button-raised {
    background-color: transparent;
    border: 1px solid #007bff;
    color: #007bff;

    &:hover {
      background-color: #e6f0ff; // Optional: light hover background
      border-color: #0056b3;
      color: #0056b3;
    }

    .pi {
      color: inherit; // Ensure icon color follows text color
    }
  }

  &.custom-button-info {
    background-color: #17a2b8;
    border-color: #17a2b8;

    &:hover {
      background-color: #117a8b;
      border-color: #117a8b;
    }
  }
}
/* styles.module.css */
.hideOnMobile {
  display: none;
}

@media (min-width: 640px) {
  .hideOnMobile {
    display: inline-flex;
  }
}


// styles/olk/olkcss.scss
/* Existing styles */
.invoice-container,
.invoice-list-container,
.invoice-view-container {
  padding: 2rem;
  background-color: #f8f9fa;

  h1 {
    margin-bottom: 1.5rem;
    color: #333;
  }
}

.invoice-table {
  .p-datatable-table {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .p-datatable-header {
    background-color: #f1f3f5;
  }
}

.p-field-radiobutton {
  margin-bottom: 1rem;

  label {
    margin-left: 0.5rem;
    color: #333;
  }

  &.radio-inline {
    display: flex;
    align-items: center;
    gap: 1rem;

    .p-radiobutton {
      margin-right: 0.5rem;
    }

    label {
      margin: 0;
    }
  }
}

.p-button {
  &.p-button-raised {
    background-color: #007bff;
   // border-color: #007bff;

    &:hover {
      background-color: #0056b3;
    //  border-color: #0056b3;
    }
  }

  &.p-button-info {
   // background-color: #17a2b8;
   // border-color: #17a2b8;

    &:hover {
     // background-color: #117a8b;
      border-color: #117a8b;
    }
  }
}

.invoice-form {
  .p-field {
    margin-bottom: 1rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #333;
    }
  }
}

/* Updated styles for InvoiceView */
.invoice-view {
  max-width: 1200px;
  margin: 0 auto;

  .p-card {
    border-radius: 12px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
    background-color: #ffffff;

    .p-card-title {
      font-size: 1.8rem;
      color: #2c3e50;
      font-weight: 600;
    }

    .p-card-subtitle {
      font-size: 1.1rem;
      color: #6c757d;
      margin-bottom: 1rem;
    }
  }

  .invoice-section {
    margin-bottom: 1.5rem;

    .p-fieldset {
      border-radius: 8px;
      background-color: #fafafa;

      .p-fieldset-legend {
        font-size: 1.2rem;
        color: #34495e;
        font-weight: 500;
        background-color: #f1f3f5;
        padding: 0.5rem 1rem;
        border-radius: 6px;
      }

      .p-field {
        margin-bottom: 0.75rem;

        label {
          color: #333;
          font-weight: 600;
          margin-bottom: 0.25rem;
        }

        span {
          color: #555;
        }
      }
    }
  }

  .p-panel {
    border-radius: 8px;
    background-color: #ffffff;

    .p-panel-header {
      background-color: #f1f3f5;
      color: #34495e;
      font-size: 1.2rem;
      font-weight: 500;
      border-radius: 8px 8px 0 0;
    }

    .p-panel-content {
      padding: 1rem;

      .p-field {
        margin-bottom: 0.75rem;

        label {
          color: #333;
          font-weight: 600;
          margin-bottom: 0.25rem;
        }

        span {
          color: #555;
        }
      }
    }
  }

  .summary-panel {
    background-color: #e9ecef;

    .p-panel-header {
      background-color: #dee2e6;
    }
  }

  .p-datatable {
    .p-datatable-table {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .p-datatable-header {
      background-color: #f1f3f5;
      color: #34495e;
    }

    .p-datatable-thead > tr > th {
      background-color: #f1f3f5;
      color: #2c3e50;
      font-weight: 600;
    }

    .p-datatable-tbody > tr {
      &:nth-child(even) {
        background-color: #f8f9fa;
      }

      &:hover {
        background-color: #e9ecef;
      }
    }

    .p-text-bold {
      font-weight: 600;
    }
  }&.p-datatable-scrollable {
  .p-datatable-wrapper {
    overflow-x: auto; /* Enable horizontal scrolling on small screens */
  }
  .p-datatable-tbody {
    display: block; /* Ensure vertical scrolling works */
  }
}

  .p-divider {
    margin: 1.5rem 0;
    background-color: #dee2e6;
  }
}

.invoice-header {
  text-align: right;
  display: flex;
  flex-direction: column;
    font-weight: 1000;
  gap: 4px; /* Adjust spacing between lines */
}