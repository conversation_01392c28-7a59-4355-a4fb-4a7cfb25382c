@media screen and (max-width: 768px) {
        .fc .fc-toolbar {
            justify-content: center;
        }

        .fc-header-toolbar {
            display: flex;
            align-items: center;
            gap: 1rem;
            justify-content: center;
            text-align: center;
            flex-wrap: wrap;

            .fc-toolbar-chunk:nth-child(2) {
                width: 100%;
                order: 0;
            }

            .fc-toolbar-chunk:nth-child(1) {
                order: 1;
            }

            .fc-toolbar-chunk:nth-child(3) {
                order: 2;
                
                .fc-button-group {
                    .fc-today-button {
                        margin-left: 2rem;
                    }
                }
            }
        }
    }
