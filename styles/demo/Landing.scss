.bg-circle {
    width: 1000px;
    height: 1000px;
    border-radius: 50%;
    background-image: linear-gradient(140deg,var(--primary-color),var(--surface-ground) 80%);
    position: absolute;
    opacity: 0.25;
    z-index: -1;
}

.visibility-hidden {
    visibility: hidden;
}

.moveinright {
    animation: moveinright .15s linear;
}

@keyframes moveinright {
    0% {
      opacity: 0;
      transform: translateX(50px);
      transition: transform .12s cubic-bezier(0, 0, 0.2, 1), opacity .12s cubic-bezier(0, 0, 0.2, 1);
    }
    100% {
      opacity: 1;
      transform: translateX(0%);
    }
  }